import React, { useState, useMemo } from 'react';
import {
  <PERSON><PERSON>ontainer,
  ProCard,
  ProTable,
} from '@ant-design/pro-components';
import {
  Button,
  Input,
  message,
  Modal,
  Form,
  Space,
  Tag,
  Typography,
  Descriptions,
  Alert,
  Spin,
  Checkbox,
  Select,
} from 'antd';
import type { ProColumns } from '@ant-design/pro-components';
import {
  SearchOutlined,
  MailOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { request } from '@umijs/max';

const { Title, Text } = Typography;
const { TextArea } = Input;

// --- Interfaces for data structures ---
interface PONotificationSummary {
    hq_po_id: string;
    po_date?: string;
    vendor_name?: string;
    master_po_total_amount?: number | string;
    master_po_total_qty?: number | string;
}

interface PONotificationItem {
  po_no?: string;
  po_branch_code?: string;
  po_branch_name?: string;
  po_delivered?: string;
  delivery_date?: string;
  supplier_delivery_note?: string;
  total_po_ordered_pcs?: number | string;
  contact_email?: string;
  hq_po_id?: string; // Add this for bulk operations
}

interface PONotificationResponse {
  success: boolean;
  message: string;
  hq_po_id?: string;
  summary?: PONotificationSummary;
  po_data?: PONotificationItem[];
}

interface BulkPONotificationResponse {
  success: boolean;
  message: string;
  results: {
    [hq_po_id: string]: PONotificationResponse;
  };
  total_found: number;
  total_searched: number;
}

const PONotificationPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [searchResult, setSearchResult] = useState<PONotificationResponse | null>(null);
  const [bulkSearchResults, setBulkSearchResults] = useState<BulkPONotificationResponse | null>(null);
  const [emailModalVisible, setEmailModalVisible] = useState(false);
  const [emailLoading, setEmailLoading] = useState(false);
  const [selectedPoNos, setSelectedPoNos] = useState<React.Key[]>([]);
  const [selectedMasterPOs, setSelectedMasterPOs] = useState<React.Key[]>([]);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [previewContent, setPreviewContent] = useState<string>('');
  const [isBulkMode, setIsBulkMode] = useState(false);
  const [searchForm] = Form.useForm();
  const [emailForm] = Form.useForm();

  // Handle search by HQ PO ID (single or bulk)
  const handleSearch = async (values: { hq_po_id: string }) => {
    if (!values.hq_po_id?.trim()) {
      message.error('Please enter HQ PO ID(s)');
      return;
    }

    const hqPoIds = values.hq_po_id.trim().split('\n').map(id => id.trim()).filter(id => id.length > 0);

    if (hqPoIds.length === 0) {
      message.error('Please enter valid HQ PO ID(s)');
      return;
    }

    // Validate that no HQ PO ID is 0 or equivalent
    const invalidIds = hqPoIds.filter(id => {
      // Remove any letter prefix and check if the numeric part is 0
      const numericPart = id.replace(/^[A-Za-z]+/, '');
      return numericPart === '0' || numericPart === '00' || numericPart === '000';
    });

    if (invalidIds.length > 0) {
      message.error(`Invalid Master PO ID(s): ${invalidIds.join(', ')}. Master PO ID cannot be 0 as it represents POs without Master PO assignment.`);
      return;
    }

    setLoading(true);
    setSearchResult(null);
    setBulkSearchResults(null);
    setSelectedPoNos([]);
    setSelectedMasterPOs([]);

    try {
      if (hqPoIds.length === 1) {
        // Single PO search
        setIsBulkMode(false);
        const response = await request<PONotificationResponse>('/api/po-import/po-notification/search', {
          method: 'POST',
          data: { hq_po_id: hqPoIds[0] },
        });
        setSearchResult(response);
        if (response.success && response.po_data && response.po_data.length > 0) {
          message.success(response.message);
          // Pre-select all POs by default
          setSelectedPoNos(response.po_data.map(po => po.po_no || 'N/A'));
        } else {
          message.warning(response.message || 'No POs found.');
        }
      } else {
        // Bulk PO search
        setIsBulkMode(true);
        const response = await request<BulkPONotificationResponse>('/api/po-import/po-notification/bulk-search', {
          method: 'POST',
          data: { hq_po_ids: hqPoIds },
        });
        setBulkSearchResults(response);
        if (response.success && response.total_found > 0) {
          message.success(`Found ${response.total_found} Master PO(s) out of ${response.total_searched} searched.`);
          // Pre-select all found Master POs
          setSelectedMasterPOs(Object.keys(response.results));
        } else {
          message.warning(response.message || 'No POs found.');
        }
      }
    } catch (error) {
      console.error('Search error:', error);
      message.error('Failed to search for POs. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Helper function to generate preview content
  const generatePreviewContent = (values: {
    custom_subject?: string;
    custom_message?: string;
    override_email?: string[];
    selectedPOs?: any[];
    summary?: any;
    hq_po_id?: string;
    isBulkPreview?: boolean;
    totalMasterPOs?: number;
  }) => {
    const selectedPOs = values.selectedPOs || (searchResult?.po_data?.filter(po => selectedPoNos.includes(po.po_no)) || []);
    const isOverrideMode = values.override_email && values.override_email.length > 0;
    const currentSummary = values.summary || searchResult?.summary;
    const currentHqPoId = values.hq_po_id || searchResult?.hq_po_id;

    // Generate subject with new format: PO Notification: [brand] - [vendor name] - [po_date] - HQ Master PO ID: [xxxxx]
    let subject = values.custom_subject;
    if (!subject) {
      const vendor_name = currentSummary?.vendor_name || 'N/A';
      const po_date = currentSummary?.po_date || 'N/A';

      if (values.isBulkPreview) {
        // For bulk preview, use the first PO's branch name
        const brand = selectedPOs[0]?.po_branch_name || 'N/A';
        subject = `PO Notification: ${brand} - ${vendor_name} - ${po_date} - HQ Master PO ID: ${currentHqPoId} (Bulk Preview - ${values.totalMasterPOs} Master POs)`;
      } else if (isOverrideMode) {
        // For override mode, determine if multiple branches
        const branchNames = [...new Set(selectedPOs.map(po => po.po_branch_name).filter(name => name))];
        const brand = branchNames.length === 1 ? branchNames[0] : 'Multiple Branches';
        subject = `PO Notification: ${brand} - ${vendor_name} - ${po_date} - HQ Master PO ID: ${currentHqPoId}`;
      } else {
        // For branch mode, use the specific branch name
        const brand = selectedPOs[0]?.po_branch_name || 'N/A';
        subject = `PO Notification: ${brand} - ${vendor_name} - ${po_date} - HQ Master PO ID: ${currentHqPoId}`;
      }
    }

    // Generate message
    let message = values.custom_message;
    if (!message) {
      if (values.isBulkPreview) {
        message = `This is a preview for Master PO ${currentHqPoId}. In bulk mode, similar emails will be sent for all ${values.totalMasterPOs} selected Master POs.\n\nPlease find the details for ${selectedPOs.length} Purchase Order(s) related to HQ PO ID: ${currentHqPoId}.`;
      } else if (isOverrideMode) {
        message = `Please find the details for ${selectedPOs.length} Purchase Order(s) related to HQ PO ID: ${currentHqPoId}.`;
      } else {
        message = `Please find details for ${selectedPOs.length} new Purchase Order(s) assigned to your branch.`;
      }
    }

    // Generate HTML preview
    return `
      <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif; line-height: 1.6; color: #333; max-width: 1200px; margin: 0 auto; padding: 20px; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);">
        <div style="padding: 0 20px;">
          <div style="font-size: 20px; margin-bottom: 20px; color: #333; font-weight: bold;">
            ${subject}
          </div>

          <div style="font-size: 16px; line-height: 1.8; margin-bottom: 30px; color: #555; white-space: pre-line;">
            ${message}
          </div>

          <div style="margin: 30px 0;">
            <h3 style="color: #1890ff; margin-bottom: 15px; font-size: 18px;">Master PO Information</h3>
            <div style="background-color: #f8f9fa; border-radius: 6px; padding: 20px; margin-bottom: 25px;">
              <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                <div>
                  <strong style="color: #333;">Master PO No:</strong><br>
                  <span style="color: #555;">${currentHqPoId || 'N/A'}</span>
                </div>
                <div>
                  <strong style="color: #333;">PO Date:</strong><br>
                  <span style="color: #555;">${currentSummary?.po_date || 'N/A'}</span>
                </div>
                <div>
                  <strong style="color: #333;">Vendor:</strong><br>
                  <span style="color: #555;">${currentSummary?.vendor_name || 'N/A'}</span>
                </div>
                <div>
                  <strong style="color: #333;">Total Quantity:</strong><br>
                  <span style="color: #555; font-weight: bold;">${currentSummary?.master_po_total_qty || 'N/A'}</span>
                </div>
              </div>
            </div>
          </div>

          <div style="margin: 30px 0;">
            <h3 style="color: #1890ff; margin-bottom: 15px; font-size: 18px;">Purchase Order Details</h3>
            <div style="overflow-x: auto; margin: 20px 0; border: 1px solid #e9ecef; border-radius: 6px;">
              <table style="width: 100%; min-width: 800px; border-collapse: collapse; font-size: 13px; background-color: #fff;">
                <thead>
                  <tr style="background-color: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                    <th style="border: 1px solid #dee2e6; padding: 12px 8px; text-align: left; font-weight: 600; color: #495057;">PO No</th>
                    <th style="border: 1px solid #dee2e6; padding: 12px 8px; text-align: left; font-weight: 600; color: #495057;">Branch Code</th>
                    <th style="border: 1px solid #dee2e6; padding: 12px 8px; text-align: left; font-weight: 600; color: #495057;">Branch Name</th>
                    <th style="border: 1px solid #dee2e6; padding: 12px 8px; text-align: left; font-weight: 600; color: #495057;">Delivery Date</th>
                    <th style="border: 1px solid #dee2e6; padding: 12px 8px; text-align: left; font-weight: 600; color: #495057;">Supplier Delivery Note</th>
                    <th style="border: 1px solid #dee2e6; padding: 12px 8px; text-align: right; font-weight: 600; color: #495057;">Total Ordered Pcs</th>
                  </tr>
                </thead>
                <tbody>
                  ${selectedPOs.map((po, index) => `
                    <tr style="background-color: ${index % 2 === 0 ? '#f8f9fa' : '#ffffff'};">
                      <td style="border: 1px solid #dee2e6; padding: 10px 8px;">${po.po_no}</td>
                      <td style="border: 1px solid #dee2e6; padding: 10px 8px;">${po.po_branch_code || 'N/A'}</td>
                      <td style="border: 1px solid #dee2e6; padding: 10px 8px;">${po.po_branch_name || 'N/A'}</td>
                      <td style="border: 1px solid #dee2e6; padding: 10px 8px;">${po.delivery_date || 'N/A'}</td>
                      <td style="border: 1px solid #dee2e6; padding: 10px 8px;">${po.supplier_delivery_note || 'N/A'}</td>
                      <td style="border: 1px solid #dee2e6; padding: 10px 8px; text-align: right; font-weight: 600; color: #28a745;">${typeof po.total_po_ordered_pcs === 'number' ? po.total_po_ordered_pcs.toLocaleString() : (po.total_po_ordered_pcs || '0')}</td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #e9ecef; text-align: center; font-size: 14px; color: #888;">
          <p>This email was automatically generated.<br>Please do not reply to this email.</p>
        </div>
      </div>
    `;
  };

  const handlePreviewEmail = () => {
    const values = emailForm.getFieldsValue();

    if (isBulkMode) {
      // Bulk mode preview
      if (selectedMasterPOs.length === 0) {
        message.error('Please select at least one Master PO to preview.');
        return;
      }

      // For bulk mode, show preview for the first selected Master PO
      const firstMasterPO = selectedMasterPOs[0] as string;
      const masterPOResult = bulkSearchResults?.results[firstMasterPO];

      if (!masterPOResult?.po_data) {
        message.error('No PO data found for preview.');
        return;
      }

      try {
        const htmlContent = generatePreviewContent({
          ...values,
          selectedPOs: masterPOResult.po_data,
          summary: masterPOResult.summary,
          hq_po_id: firstMasterPO,
          isBulkPreview: true,
          totalMasterPOs: selectedMasterPOs.length
        });
        setPreviewContent(htmlContent);
        setPreviewModalVisible(true);
      } catch (error) {
        console.error('Error generating bulk preview:', error);
        message.error('Failed to generate email preview. Please try again.');
      }
    } else {
      // Single mode preview
      const selectedPOs = searchResult?.po_data?.filter(po => selectedPoNos.includes(po.po_no || 'N/A')) || [];

      if (selectedPOs.length === 0) {
        message.error('Please select at least one PO to preview.');
        return;
      }

      try {
        const htmlContent = generatePreviewContent(values);
        setPreviewContent(htmlContent);
        setPreviewModalVisible(true);
      } catch (error) {
        console.error('Error generating preview:', error);
        message.error('Failed to generate email preview. Please try again.');
      }
    }
  };

  const handleSendEmail = async (values: {
    cc_emails?: string[];
    override_email?: string[];
    custom_subject?: string;
    custom_message?: string;
  }) => {
    if (isBulkMode) {
      // Bulk email sending
      if (selectedMasterPOs.length === 0) {
        message.error('Please select at least one Master PO to send notifications for.');
        return;
      }

      setEmailLoading(true);
      try {
        const response = await request('/api/po-import/po-notification/bulk-send-email', {
          method: 'POST',
          data: {
            hq_po_ids: selectedMasterPOs,
            bulk_results: bulkSearchResults,
            cc_emails: values.cc_emails,
            override_email: values.override_email,
            custom_subject: values.custom_subject,
            custom_message: values.custom_message,
          },
        });
        if (response.success) {
          message.success(response.message);
          setEmailModalVisible(false);
        } else {
          message.error(response.message || 'Failed to send emails.');
        }
      } catch (error) {
        console.error('Bulk email sending error:', error);
        message.error('An unexpected error occurred while sending emails.');
      } finally {
        setEmailLoading(false);
      }
    } else {
      // Single PO email sending
      const selectedPOs = searchResult?.po_data?.filter(po => selectedPoNos.includes(po.po_no || 'N/A')) || [];
      if (selectedPOs.length === 0) {
        message.error('Please select at least one PO to send notifications for.');
        return;
      }

      setEmailLoading(true);
      try {
        const response = await request('/api/po-import/po-notification/send-email', {
          method: 'POST',
          data: {
            hq_po_id: searchResult?.hq_po_id,
            po_data: selectedPOs,
            cc_emails: values.cc_emails,
            override_email: values.override_email,
            summary: searchResult?.summary,
            custom_subject: values.custom_subject,
            custom_message: values.custom_message,
          },
        });
        if (response.success) {
          message.success(response.message);
          setEmailModalVisible(false);
        } else {
          message.error(response.message || 'Failed to send emails.');
        }
      } catch (error) {
        console.error('Email sending error:', error);
        message.error('An unexpected error occurred while sending emails.');
      } finally {
        setEmailLoading(false);
      }
    }
  };
  
  const getStatusColor = (status: string | undefined): string => {
    if (!status) return 'default';
    const statusLower = status.toLowerCase();
    if (statusLower.includes('confirmed') || (statusLower.includes('delivered') && !statusLower.includes('no delivered')) || statusLower.includes('approved')) {
      return 'green';
    }
    if (statusLower.includes('no delivered') || statusLower.includes('no approval') || statusLower.includes('saved')) {
      return 'orange';
    }
    if (statusLower.includes('rejected') || statusLower.includes('cancelled') || statusLower.includes('terminated')) {
      return 'red';
    }
    return 'default';
  };

  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      setSelectedPoNos(searchResult?.po_data?.map(po => po.po_no || 'N/A') || []);
    } else {
      setSelectedPoNos([]);
    }
  };

  const handleSelectRow = (poNo: string | undefined, checked: boolean) => {
    const safePoNo = poNo || 'N/A';
    setSelectedPoNos(prev => {
      if (checked) {
        return [...prev, safePoNo];
      } else {
        return prev.filter(p => p !== safePoNo);
      }
    });
  };

  const columns: ProColumns<PONotificationItem>[] = [
    {
      title: <Checkbox
        indeterminate={selectedPoNos.length > 0 && selectedPoNos.length < (searchResult?.po_data?.length || 0)}
        checked={selectedPoNos.length === (searchResult?.po_data?.length || 0) && (searchResult?.po_data?.length || 0) > 0}
        onChange={handleSelectAll}
      />,
      dataIndex: 'select',
      width: 50,
      render: (_, record) => (
        <Checkbox
          checked={selectedPoNos.includes(record.po_no || 'N/A')}
          onChange={(e) => handleSelectRow(record.po_no, e.target.checked)}
        />
      ),
    },
    {
      title: 'PO No',
      dataIndex: 'po_no',
      key: 'po_no',
      width: 120,
      sorter: (a, b) => (a.po_no || 'N/A').localeCompare(b.po_no || 'N/A'),
    },
    {
      title: 'Branch Code',
      dataIndex: 'po_branch_code',
      key: 'po_branch_code',
      width: 100,
      sorter: (a, b) => (a.po_branch_code || '').localeCompare(b.po_branch_code || ''),
    },
    {
      title: 'Branch Name',
      dataIndex: 'po_branch_name',
      key: 'po_branch_name',
      width: 150,
      sorter: (a, b) => (a.po_branch_name || '').localeCompare(b.po_branch_name || ''),
    },
    {
      title: 'Delivered',
      dataIndex: 'po_delivered',
      key: 'po_delivered',
      width: 100,
      render: (delivered: string) => (
        <Tag color={delivered === 'DELIVERED' ? 'green' : 'orange'}>
          {delivered || 'N/A'}
        </Tag>
      ),
    },
    {
      title: 'Delivery Date',
      dataIndex: 'delivery_date',
      key: 'delivery_date',
      width: 120,
      sorter: (a, b) => {
        const dateA = a.delivery_date ? new Date(a.delivery_date).getTime() : 0;
        const dateB = b.delivery_date ? new Date(b.delivery_date).getTime() : 0;
        return dateA - dateB;
      },
    },
    {
      title: 'Supplier Delivery Note',
      dataIndex: 'supplier_delivery_note',
      key: 'supplier_delivery_note',
      width: 200,
      ellipsis: true,
      render: (note: string) => note || 'N/A',
    },
    {
      title: 'Total Ordered Pcs',
      dataIndex: 'total_po_ordered_pcs',
      key: 'total_po_ordered_pcs',
      width: 150,
      align: 'right' as const,
      render: (value: number | string) => {
        if (typeof value === 'string' && value.includes('No View Permission')) {
          return <span style={{ color: '#ff4d4f' }}>{value}</span>;
        }
        return typeof value === 'number' ? value.toLocaleString() : (value || '0');
      },
    },
    {
      title: 'Contact Email',
      dataIndex: 'contact_email',
      key: 'contact_email',
      width: 200,
      ellipsis: true,
    },
  ];

  const emailModalColumns: ProColumns<PONotificationItem>[] = [
    { title: 'PO No', dataIndex: 'po_no', key: 'po_no' },
    { title: 'Branch', dataIndex: 'po_branch_name', key: 'po_branch_name' },
    { title: 'Branch Email', dataIndex: 'contact_email', key: 'contact_email', render: (text) => text || <Text type="danger">No Email</Text>},
  ];

  return (
    <PageContainer
      header={{
        title: 'Purchase Order Notification',
        extra: [
            <Button
              key="send-email"
              type="primary"
              icon={<MailOutlined />}
              onClick={() => setEmailModalVisible(true)}
              loading={emailLoading}
              disabled={
                (!searchResult?.po_data || searchResult.po_data.length === 0) &&
                (!bulkSearchResults?.results || Object.keys(bulkSearchResults.results).length === 0)
              }
            >
              Send Email Notifications
            </Button>
        ],
      }}
    >
      <ProCard>
        <Form form={searchForm} onFinish={handleSearch} layout="vertical">
          <Form.Item
            name="hq_po_id"
            label="Master PO No (HQ PO ID)"
            rules={[{ required: true, message: 'Please input the HQ PO ID(s)!' }]}
            help="Enter exact HQ PO ID(s) for precise matching. Single ID or multiple IDs separated by new lines for bulk search. Note: Master PO ID cannot be 0."
          >
            <TextArea
              placeholder="Enter exact HQ PO ID(s) for precise matching&#10;Single: HQ34921 or 34921&#10;Multiple (one per line):&#10;HQ34921&#10;HQ34922&#10;HQ34923&#10;&#10;Note: Search is exact match only&#10;Master PO ID cannot be 0"
              style={{ width: '100%' }}
              rows={4}
              showCount
              maxLength={1000}
            />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" icon={<SearchOutlined />} loading={loading}>
              Search
            </Button>
          </Form.Item>
        </Form>
      </ProCard>

      <Spin spinning={loading}>
        {searchResult && searchResult.success && searchResult.summary && (
          <ProCard title="Master PO Information" style={{ marginTop: 20 }} bordered>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="Master PO No">{searchResult.summary.hq_po_id}</Descriptions.Item>
              <Descriptions.Item label="PO Date">{searchResult.summary.po_date}</Descriptions.Item>
              <Descriptions.Item label="Vendor">{searchResult.summary.vendor_name}</Descriptions.Item>
              <Descriptions.Item label="Master PO Total Qty">
                <strong>{typeof searchResult.summary.master_po_total_qty === 'number' ? searchResult.summary.master_po_total_qty.toLocaleString() : searchResult.summary.master_po_total_qty}</strong>
              </Descriptions.Item>
            </Descriptions>
          </ProCard>
        )}

        {searchResult && !searchResult.success && (
            <Alert
                message="Search Information"
                description={searchResult.message}
                type="warning"
                showIcon
                style={{ marginTop: 20 }}
            />
        )}

        {/* Bulk Search Results */}
        {bulkSearchResults && bulkSearchResults.success && (
          <ProCard title={`Bulk Search Results (${bulkSearchResults.total_found}/${bulkSearchResults.total_searched} found)`} style={{ marginTop: 20 }} bordered>
            <ProTable<{hq_po_id: string; summary: PONotificationSummary; po_count: number}>
              headerTitle="Master PO Selection"
              dataSource={Object.entries(bulkSearchResults.results).map(([hq_po_id, result]) => ({
                hq_po_id,
                summary: result.summary!,
                po_count: result.po_data?.length || 0
              }))}
              columns={[
                {
                  title: 'Master PO No',
                  dataIndex: 'hq_po_id',
                  key: 'hq_po_id',
                  width: 150,
                },
                {
                  title: 'PO Date',
                  dataIndex: ['summary', 'po_date'],
                  key: 'po_date',
                  width: 120,
                },
                {
                  title: 'Vendor',
                  dataIndex: ['summary', 'vendor_name'],
                  key: 'vendor_name',
                  width: 200,
                },
                {
                  title: 'Total Qty',
                  dataIndex: ['summary', 'master_po_total_qty'],
                  key: 'master_po_total_qty',
                  width: 120,
                  render: (qty) => typeof qty === 'number' ? qty.toLocaleString() : qty,
                },
                {
                  title: 'Branch POs',
                  dataIndex: 'po_count',
                  key: 'po_count',
                  width: 100,
                  render: (count) => <Tag color="blue">{count}</Tag>,
                },
              ]}
              rowKey="hq_po_id"
              rowSelection={{
                selectedRowKeys: selectedMasterPOs,
                onChange: (selectedRowKeys) => setSelectedMasterPOs(selectedRowKeys),
              }}
              pagination={false}
              search={false}
              toolBarRender={false}
              size="small"
            />
          </ProCard>
        )}

        {bulkSearchResults && !bulkSearchResults.success && (
            <Alert
                message="Bulk Search Information"
                description={bulkSearchResults.message}
                type="warning"
                showIcon
                style={{ marginTop: 20 }}
            />
        )}

        {searchResult && searchResult.po_data && (
          <ProCard style={{ marginTop: 20 }}>
            <ProTable<PONotificationItem>
              headerTitle="Branch PO Details"
              dataSource={searchResult.po_data}
              columns={columns}
              rowKey="po_no"
              pagination={{ pageSize: 10 }}
              search={false}
              toolBarRender={false}
              scroll={{ x: 'max-content' }}
            />
          </ProCard>
        )}
      </Spin>

      <Modal
        title="Email Notification Options"
        open={emailModalVisible}
        onCancel={() => setEmailModalVisible(false)}
        confirmLoading={emailLoading}
        width={800}
        destroyOnClose
        footer={[
          <Button key="cancel" onClick={() => setEmailModalVisible(false)}>
            Cancel
          </Button>,
          <Button key="preview" icon={<EyeOutlined />} onClick={handlePreviewEmail}>
            Preview Email
          </Button>,
          <Button key="send" type="primary" loading={emailLoading} onClick={() => emailForm.submit()}>
            Send Email
          </Button>,
        ]}
      >
        <Form form={emailForm} layout="vertical" onFinish={handleSendEmail} initialValues={{ cc_emails: [], override_email: [], custom_subject: '', custom_message: '' }}>
          <ProTable<PONotificationItem>
            headerTitle="Select Branches to Notify"
            dataSource={searchResult?.po_data || []}
            columns={emailModalColumns}
            rowKey="po_no"
            rowSelection={{
              selectedRowKeys: selectedPoNos,
              onChange: (selectedRowKeys) => setSelectedPoNos(selectedRowKeys),
            }}
            pagination={false}
            search={false}
            toolBarRender={false}
            size="small"
          />
          <Form.Item 
            name="cc_emails" 
            label="CC Recipients" 
            style={{ marginTop: 20 }}
            rules={[{
              validator: async (_, value) => {
                if (value && value.length > 0) {
                  for (const email of value) {
                    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                      return Promise.reject(new Error(`Invalid email address: ${email}`));
                    }
                  }
                }
                return Promise.resolve();
              }
            }]}
          >
            <Select
              mode="tags"
              placeholder="Enter CC email addresses and press Enter"
              tokenSeparators={[',', ' ', '\n']}
              allowClear
              open={false}
              onInputKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                }
              }}
            />
          </Form.Item>
          <Form.Item
            name="override_email"
            label="Override Recipient"
            help="If filled, a single email with all selected POs will be sent to these addresses."
            rules={[{
              validator: async (_, value) => {
                if (value && value.length > 0) {
                  for (const email of value) {
                    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                      return Promise.reject(new Error(`Invalid email address: ${email}`));
                    }
                  }
                }
                return Promise.resolve();
              }
            }]}
          >
            <Select
              mode="tags"
              placeholder="Enter one or more email addresses to override"
              tokenSeparators={[',', ' ', '\n']}
              allowClear
              open={false}
              onInputKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                }
              }}
            />
          </Form.Item>
          <Form.Item
            name="custom_subject"
            label="Custom Email Subject"
            help="If left empty, a default subject will be used."
          >
            <Input placeholder="Enter custom email subject (optional)" />
          </Form.Item>
          <Form.Item
            name="custom_message"
            label="Custom Message"
            help="This message will appear before the PO details table in the email. If left empty, a default message will be used."
          >
            <TextArea
              rows={4}
              placeholder="Enter custom message to include in the email (optional)"
              showCount
              maxLength={1000}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* Email Preview Modal */}
      <Modal
        title="Email Preview"
        open={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPreviewModalVisible(false)}>
            Close
          </Button>,
          <Button key="send" type="primary" onClick={() => {
            setPreviewModalVisible(false);
            emailForm.submit();
          }}>
            Send Email
          </Button>,
        ]}
        width={1000}
        style={{ top: 20 }}
        zIndex={10000}
        maskClosable={false}
        destroyOnClose
        getContainer={false}
        mask={true}
        maskStyle={{ zIndex: 9999 }}
      >
        <Alert
          message="Email Preview"
          description="This is how your email will appear to recipients. You can review the subject, custom message, and PO details before sending."
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        <div style={{ maxHeight: '65vh', overflow: 'auto', border: '1px solid #d9d9d9', borderRadius: '6px', padding: '16px', backgroundColor: '#fafafa' }}>
          <div dangerouslySetInnerHTML={{ __html: previewContent }} />
        </div>
      </Modal>
    </PageContainer>
  );
};

export default PONotificationPage; 