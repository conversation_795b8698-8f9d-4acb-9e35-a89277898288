import React, { useState, useMemo } from 'react';
import {
  <PERSON><PERSON>ontainer,
  ProCard,
  ProTable,
} from '@ant-design/pro-components';
import {
  Button,
  Input,
  message,
  Modal,
  Form,
  Space,
  Tag,
  Typography,
  Descriptions,
  Alert,
  Spin,
  Checkbox,
  Select,
} from 'antd';
import type { ProColumns } from '@ant-design/pro-components';
import {
  SearchOutlined,
  MailOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { request } from '@umijs/max';

const { Title, Text } = Typography;
const { TextArea } = Input;

// --- Interfaces for data structures ---
interface PONotificationSummary {
    hq_po_id: string;
    po_date?: string;
    vendor_name?: string;
    master_po_total_amount?: number | string;
    master_po_total_qty?: number | string;
}

interface PONotificationItem {
  po_no: string;
  po_branch_code?: string;
  po_branch_name?: string;
  po_delivered?: string;
  delivery_date?: string;
  supplier_delivery_note?: string;
  total_po_ordered_pcs?: number | string;
  contact_email?: string;
}

interface PONotificationResponse {
  success: boolean;
  message: string;
  hq_po_id?: string;
  summary?: PONotificationSummary;
  po_data?: PONotificationItem[];
}

const PONotificationPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [searchResult, setSearchResult] = useState<PONotificationResponse | null>(null);
  const [emailModalVisible, setEmailModalVisible] = useState(false);
  const [emailLoading, setEmailLoading] = useState(false);
  const [selectedPoNos, setSelectedPoNos] = useState<React.Key[]>([]);
  const [searchForm] = Form.useForm();
  const [emailForm] = Form.useForm();

  // Handle search by HQ PO ID
  const handleSearch = async (values: { hq_po_id: string }) => {
    if (!values.hq_po_id?.trim()) {
      message.error('Please enter an HQ PO ID');
      return;
    }
    setLoading(true);
    setSearchResult(null);
    setSelectedPoNos([]);
    try {
      const response = await request<PONotificationResponse>('/api/po-import/po-notification/search', {
        method: 'POST',
        data: { hq_po_id: values.hq_po_id.trim() },
      });
      setSearchResult(response);
      if (response.success && response.po_data && response.po_data.length > 0) {
        message.success(response.message);
        // Pre-select all POs by default
        setSelectedPoNos(response.po_data.map(po => po.po_no));
      } else {
        message.warning(response.message || 'No POs found.');
      }
    } catch (error) {
      console.error('Search error:', error);
      message.error('Failed to search for POs. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSendEmail = async (values: {
    cc_emails?: string[];
    override_email?: string;
    custom_subject?: string;
    custom_message?: string;
  }) => {
    const selectedPOs = searchResult?.po_data?.filter(po => selectedPoNos.includes(po.po_no)) || [];
    if (selectedPOs.length === 0) {
      message.error('Please select at least one PO to send notifications for.');
      return;
    }

    setEmailLoading(true);
    try {
      const response = await request('/api/po-import/po-notification/send-email', {
        method: 'POST',
        data: {
          hq_po_id: searchResult?.hq_po_id,
          po_data: selectedPOs,
          cc_emails: values.cc_emails,
          override_email: values.override_email,
          summary: searchResult?.summary,
          custom_subject: values.custom_subject,
          custom_message: values.custom_message,
        },
      });
      if (response.success) {
        message.success(response.message);
        setEmailModalVisible(false);
      } else {
        message.error(response.message || 'Failed to send emails.');
      }
    } catch (error) {
      console.error('Email sending error:', error);
      message.error('An unexpected error occurred while sending emails.');
    } finally {
      setEmailLoading(false);
    }
  };
  
  const getStatusColor = (status: string | undefined): string => {
    if (!status) return 'default';
    const statusLower = status.toLowerCase();
    if (statusLower.includes('confirmed') || (statusLower.includes('delivered') && !statusLower.includes('no delivered')) || statusLower.includes('approved')) {
      return 'green';
    }
    if (statusLower.includes('no delivered') || statusLower.includes('no approval') || statusLower.includes('saved')) {
      return 'orange';
    }
    if (statusLower.includes('rejected') || statusLower.includes('cancelled') || statusLower.includes('terminated')) {
      return 'red';
    }
    return 'default';
  };

  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      setSelectedPoNos(searchResult?.po_data?.map(po => po.po_no) || []);
    } else {
      setSelectedPoNos([]);
    }
  };

  const handleSelectRow = (poNo: string, checked: boolean) => {
    setSelectedPoNos(prev => {
      if (checked) {
        return [...prev, poNo];
      } else {
        return prev.filter(p => p !== poNo);
      }
    });
  };

  const columns: ProColumns<PONotificationItem>[] = [
    {
      title: <Checkbox
        indeterminate={selectedPoNos.length > 0 && selectedPoNos.length < (searchResult?.po_data?.length || 0)}
        checked={selectedPoNos.length === (searchResult?.po_data?.length || 0) && (searchResult?.po_data?.length || 0) > 0}
        onChange={handleSelectAll}
      />,
      dataIndex: 'select',
      width: 50,
      render: (_, record) => (
        <Checkbox
          checked={selectedPoNos.includes(record.po_no)}
          onChange={(e) => handleSelectRow(record.po_no, e.target.checked)}
        />
      ),
    },
    {
      title: 'PO No',
      dataIndex: 'po_no',
      key: 'po_no',
      width: 120,
      sorter: (a, b) => a.po_no.localeCompare(b.po_no),
    },
    {
      title: 'Branch Code',
      dataIndex: 'po_branch_code',
      key: 'po_branch_code',
      width: 100,
      sorter: (a, b) => (a.po_branch_code || '').localeCompare(b.po_branch_code || ''),
    },
    {
      title: 'Branch Name',
      dataIndex: 'po_branch_name',
      key: 'po_branch_name',
      width: 150,
      sorter: (a, b) => (a.po_branch_name || '').localeCompare(b.po_branch_name || ''),
    },
    {
      title: 'Delivered',
      dataIndex: 'po_delivered',
      key: 'po_delivered',
      width: 100,
      render: (delivered: string) => (
        <Tag color={delivered === 'DELIVERED' ? 'green' : 'orange'}>
          {delivered || 'N/A'}
        </Tag>
      ),
    },
    {
      title: 'Delivery Date',
      dataIndex: 'delivery_date',
      key: 'delivery_date',
      width: 120,
      sorter: (a, b) => {
        const dateA = a.delivery_date ? new Date(a.delivery_date).getTime() : 0;
        const dateB = b.delivery_date ? new Date(b.delivery_date).getTime() : 0;
        return dateA - dateB;
      },
    },
    {
      title: 'Supplier Delivery Note',
      dataIndex: 'supplier_delivery_note',
      key: 'supplier_delivery_note',
      width: 200,
      ellipsis: true,
      render: (note: string) => note || 'N/A',
    },
    {
      title: 'Total Ordered Pcs',
      dataIndex: 'total_po_ordered_pcs',
      key: 'total_po_ordered_pcs',
      width: 150,
      align: 'right' as const,
      render: (value: number | string) => {
        if (typeof value === 'string' && value.includes('No View Permission')) {
          return <span style={{ color: '#ff4d4f' }}>{value}</span>;
        }
        return typeof value === 'number' ? value.toLocaleString() : (value || '0');
      },
    },
    {
      title: 'Contact Email',
      dataIndex: 'contact_email',
      key: 'contact_email',
      width: 200,
      ellipsis: true,
    },
  ];

  const emailModalColumns: ProColumns<PONotificationItem>[] = [
    { title: 'PO No', dataIndex: 'po_no', key: 'po_no' },
    { title: 'Branch', dataIndex: 'po_branch_name', key: 'po_branch_name' },
    { title: 'Branch Email', dataIndex: 'contact_email', key: 'contact_email', render: (text) => text || <Text type="danger">No Email</Text>},
  ];

  return (
    <PageContainer
      header={{
        title: 'Purchase Order Notification',
        extra: [
            <Button
              key="send-email"
              type="primary"
              icon={<MailOutlined />}
              onClick={() => setEmailModalVisible(true)}
              loading={emailLoading}
              disabled={!searchResult?.po_data || searchResult.po_data.length === 0}
            >
              Send Email Notifications
            </Button>
        ],
      }}
    >
      <ProCard>
        <Form form={searchForm} onFinish={handleSearch} layout="inline">
          <Form.Item name="hq_po_id" label="Master PO No (HQ PO ID)" rules={[{ required: true, message: 'Please input the HQ PO ID!' }]}>
            <Input placeholder="Enter HQ PO ID (e.g., HQ34921 or 34921)" style={{ width: 300 }} />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" icon={<SearchOutlined />} loading={loading}>
              Search
            </Button>
          </Form.Item>
        </Form>
      </ProCard>

      <Spin spinning={loading}>
        {searchResult && searchResult.success && searchResult.summary && (
          <ProCard title="Master PO Information" style={{ marginTop: 20 }} bordered>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="Master PO No">{searchResult.summary.hq_po_id}</Descriptions.Item>
              <Descriptions.Item label="PO Date">{searchResult.summary.po_date}</Descriptions.Item>
              <Descriptions.Item label="Vendor">{searchResult.summary.vendor_name}</Descriptions.Item>
              <Descriptions.Item label="Master PO Total Qty">
                <strong>{typeof searchResult.summary.master_po_total_qty === 'number' ? searchResult.summary.master_po_total_qty.toLocaleString() : searchResult.summary.master_po_total_qty}</strong>
              </Descriptions.Item>
            </Descriptions>
          </ProCard>
        )}

        {searchResult && !searchResult.success && (
            <Alert
                message="Search Information"
                description={searchResult.message}
                type="warning"
                showIcon
                style={{ marginTop: 20 }}
            />
        )}

        {searchResult && searchResult.po_data && (
          <ProCard style={{ marginTop: 20 }}>
            <ProTable<PONotificationItem>
              headerTitle="Branch PO Details"
              dataSource={searchResult.po_data}
              columns={columns}
              rowKey="po_no"
              pagination={{ pageSize: 10 }}
              search={false}
              toolBarRender={false}
              scroll={{ x: 'max-content' }}
            />
          </ProCard>
        )}
      </Spin>

      <Modal
        title="Email Notification Options"
        open={emailModalVisible}
        onCancel={() => setEmailModalVisible(false)}
        onOk={() => emailForm.submit()}
        confirmLoading={emailLoading}
        width={800}
        destroyOnClose
      >
        <Form form={emailForm} layout="vertical" onFinish={handleSendEmail} initialValues={{ cc_emails: [], override_email: '', custom_subject: '', custom_message: '' }}>
          <ProTable<PONotificationItem>
            headerTitle="Select Branches to Notify"
            dataSource={searchResult?.po_data || []}
            columns={emailModalColumns}
            rowKey="po_no"
            rowSelection={{
              selectedRowKeys: selectedPoNos,
              onChange: (selectedRowKeys) => setSelectedPoNos(selectedRowKeys),
            }}
            pagination={false}
            search={false}
            toolBarRender={false}
            size="small"
          />
          <Form.Item 
            name="cc_emails" 
            label="CC Recipients" 
            style={{ marginTop: 20 }}
            rules={[{
              validator: async (_, value) => {
                if (value && value.length > 0) {
                  for (const email of value) {
                    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                      return Promise.reject(new Error(`Invalid email address: ${email}`));
                    }
                  }
                }
                return Promise.resolve();
              }
            }]}
          >
            <Select mode="tags" placeholder="Enter CC email addresses and press Enter" tokenSeparators={[',', ' ']} />
          </Form.Item>
          <Form.Item
            name="override_email"
            label="Override Recipient"
            help="If filled, a single email with all selected POs will be sent to these addresses."
            rules={[{
              validator: async (_, value) => {
                if (value && value.length > 0) {
                  for (const email of value) {
                    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                      return Promise.reject(new Error(`Invalid email address: ${email}`));
                    }
                  }
                }
                return Promise.resolve();
              }
            }]}
          >
            <Select mode="tags" placeholder="Enter one or more email addresses to override" tokenSeparators={[',', ' ']} />
          </Form.Item>
          <Form.Item
            name="custom_subject"
            label="Custom Email Subject"
            help="If left empty, a default subject will be used."
          >
            <Input placeholder="Enter custom email subject (optional)" />
          </Form.Item>
          <Form.Item
            name="custom_message"
            label="Custom Message"
            help="This message will appear before the PO details table in the email. If left empty, a default message will be used."
          >
            <TextArea
              rows={4}
              placeholder="Enter custom message to include in the email (optional)"
              showCount
              maxLength={1000}
            />
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default PONotificationPage; 