#!/usr/bin/env python3
"""
Test script to verify the SKU sales summary and QOH fixes
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from tools.sku_checker_tool import get_sku_sales_summary
from tools.qoh_service import fetch_qoh_data

def test_sales_summary():
    """Test the sales summary function with sample data"""
    
    print("=== Testing Sales Summary Fix ===")
    
    # Test with a known artno and mcode
    artno = "FM9932"
    mcode = "9074514"
    
    # Create a test user context
    user_context = {
        "user_id": 2,
        "role": "user",
        "is_admin": False,
        "permitted_branches": ["MELM", "HQFA", "CURVE"]  # Sample branches
    }
    
    print(f"Testing sales summary for artno: {artno}, mcode: {mcode}")
    
    try:
        result = get_sku_sales_summary(artno, mcode, user_context)
        
        print(f"✓ Result keys: {list(result.keys())}")
        print(f"✓ Success: {result.get('success')}")
        print(f"✓ Message: {result.get('message')}")
        print(f"✓ Total qty sold: {result.get('total_qty_sold')}")
        print(f"✓ Total sales amount: {result.get('total_sales_amount')}")
        print(f"✓ Branch count: {result.get('branch_count')}")
        
        if result.get('branch_details'):
            print(f"✓ Sample branch detail: {result['branch_details'][0]}")
            # Check if branch detail has the correct fields
            sample_branch = result['branch_details'][0]
            required_fields = ['branch_code', 'branch_name', 'total_qty', 'total_sales']
            for field in required_fields:
                if field in sample_branch:
                    print(f"  ✓ {field}: {sample_branch[field]}")
                else:
                    print(f"  ✗ Missing field: {field}")
            
        return result
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_qoh_functionality():
    """Test the QOH functionality with sample data"""
    
    print("\n=== Testing QOH Fix ===")
    
    # Test with a known artno and mcode
    artno = "FM9932"
    mcode = "9074514"
    
    # Create a test user context
    user_context = {
        "user_id": 2,
        "role": "user",
        "is_admin": False,
        "permitted_branches": ["MELM", "HQFA", "CURVE"]  # Sample branches
    }
    
    print(f"Testing QOH for artno: {artno}, mcode: {mcode}")
    
    try:
        result = fetch_qoh_data(artno=artno, mcode=mcode, user_context=user_context)
        
        print(f"✓ Success: {result.success}")
        print(f"✓ Message: {result.message}")
        print(f"✓ Total records: {result.total_records}")
        print(f"✓ Table name: {result.table_name}")
        
        if result.items:
            print(f"✓ Sample QOH item: {result.items[0]}")
            # Check if QOH item has the correct fields
            sample_item = result.items[0]
            required_fields = ['branch_code', 'branch_name', 'artno', 'mcode', 'qty']
            for field in required_fields:
                if hasattr(sample_item, field):
                    print(f"  ✓ {field}: {getattr(sample_item, field)}")
                else:
                    print(f"  ✗ Missing field: {field}")
            
        return result
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("Testing SKU Checker and QOH fixes...")
    
    # Test sales summary
    sales_result = test_sales_summary()
    
    # Test QOH
    qoh_result = test_qoh_functionality()
    
    print("\n=== Summary ===")
    print(f"Sales Summary Test: {'✓ PASSED' if sales_result and sales_result.get('success') else '✗ FAILED'}")
    print(f"QOH Test: {'✓ PASSED' if qoh_result and qoh_result.success else '✗ FAILED'}") 