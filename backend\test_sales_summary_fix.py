#!/usr/bin/env python3
"""
Test script to verify the SKU sales summary fix
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from tools.sku_checker_tool import get_sku_sales_summary

def test_sales_summary():
    """Test the sales summary function with sample data"""
    
    # Test with a known artno and mcode
    artno = "FM9932"
    mcode = "4062054763290"
    
    # Create a test user context
    user_context = {
        "user_id": 2,
        "role": "user",
        "is_admin": False,
        "permitted_branches": ["MELM", "HQFA", "CURVE"]  # Sample branches
    }
    
    print(f"Testing sales summary for artno: {artno}, mcode: {mcode}")
    
    try:
        result = get_sku_sales_summary(artno, mcode, user_context)
        
        print(f"Result keys: {list(result.keys())}")
        print(f"Success: {result.get('success')}")
        print(f"Message: {result.get('message')}")
        print(f"Total qty sold: {result.get('total_qty_sold')}")
        print(f"Total sales amount: {result.get('total_sales_amount')}")
        print(f"Branch count: {result.get('branch_count')}")
        
        if result.get('branch_details'):
            print(f"Sample branch detail: {result['branch_details'][0]}")
            
        return result
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_sales_summary() 