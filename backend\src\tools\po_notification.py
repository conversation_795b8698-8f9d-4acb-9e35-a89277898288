"""
PO Notification Tool

This module handles PO notification functionality including:
- Searching for POs by HQ PO ID
- Sending email notifications for PO creation
- Email logging and tracking
"""

import logging
from typing import List, Dict, Any, Optional, Union
import mysql.connector
from ..utils.db_connections import get_mysql_connection
from ..auth.dependencies import filter_cost_fields_from_data
from datetime import date, datetime
from decimal import Decimal
from pydantic import BaseModel

# Import Pydantic models
from ..models import PONotificationItem, PONotificationSummary

logger = logging.getLogger(__name__)

def _apply_branch_filters_to_po_notification_query(
    query_parts: List[str],
    params: list,
    user_context: Dict[str, Any],
    po_branch_field: str = "brh.code"
) -> tuple[List[str], list]:
    """
    Applies branch permission filters to a query for the PO Notification.
    If the user is not admin, it ensures the PO's branch is in permitted_branches.
    Updated for MySQL syntax.
    """
    if not user_context.get('is_admin', False):
        permitted_branches = user_context.get('permitted_branches')
        if permitted_branches is not None:
            if not permitted_branches:
                logger.info(f"User {user_context.get('user_id')} is not admin and has no permitted branches. Restricting query.")
                query_parts.append("1=0") 
            else:
                # For MySQL, use IN clause instead of ANY
                placeholders = ','.join(['%s'] * len(permitted_branches))
                query_parts.append(f'{po_branch_field} IN ({placeholders})')
                params.extend(list(permitted_branches))
        else:
            logger.warning(f"User {user_context.get('user_id')} is not admin and 'permitted_branches' is None. Restricting query.")
            query_parts.append("1=0")
    return query_parts, params


def get_pos_by_hq_po_id(db: mysql.connector.MySQLConnection, hq_po_id: str, user_context: Dict[str, Any]) -> Dict[str, Any]:
    """
    Search for POs by HQ PO ID (Master PO No.)
    
    Args:
        db: MySQL database connection
        hq_po_id: HQ PO ID (Master PO No.) to search for
        user_context: User context for permissions
        
    Returns:
        A dictionary containing summary and detailed PO data, or an empty dict if not found
    """
    cursor = None
    try:
        # Clean and prepare the HQ PO ID for exact matching
        # Remove any whitespace and convert to string
        clean_hq_po_id = str(hq_po_id).strip()

        # Try to extract numeric part if it starts with letters (like "HQ34921")
        import re
        if re.match(r'^[A-Za-z]+\d+$', clean_hq_po_id):
            # Extract numeric part from format like "HQ34921"
            numeric_match = re.search(r'(\d+)', clean_hq_po_id)
            if numeric_match:
                search_hq_po_id = int(numeric_match.group(1))  # Convert to int for exact match
            else:
                logger.warning(f"Could not extract numeric part from HQ PO ID: {hq_po_id}")
                return {}
        else:
            # For pure numeric input, convert to int for exact database matching
            try:
                search_hq_po_id = int(clean_hq_po_id)
            except ValueError:
                logger.warning(f"Invalid HQ PO ID format: {hq_po_id}")
                return {}

        logger.info(f"Searching for exact HQ PO ID: {search_hq_po_id} (original input: '{hq_po_id}')")
        
        # Base query following PO checker pattern with proper JOINs
        select_clause = """
        SELECT
            COALESCE(p.po_no, 'N/A') as po_no,
            DATE_FORMAT(p.po_date, '%Y-%m-%d') as po_date,
            p.hq_po_id,
            COALESCE(brh.code, 'N/A') as po_branch_code,
            COALESCE(brh.description, 'N/A') as po_branch_name,
            CASE
                WHEN p.delivered = 1 THEN 'DELIVERED'
                WHEN p.delivered = 0 THEN 'NO DELIVERED'
                ELSE 'UNKNOWN'
            END as po_delivered,
            CASE
                WHEN p.delivery_date IS NOT NULL AND p.delivery_date != '' THEN
                    DATE_FORMAT(STR_TO_DATE(p.delivery_date, '%d/%m/%Y'), '%Y-%m-%d')
                ELSE NULL
            END as delivery_date,
            COALESCE(
                convert(
                    cast(
                        trim(both ' ' from
                            substring_index(
                                substring_index(p.remark, ':"', -1),
                                '";',
                                1
                            )
                        ) as binary
                    ) using utf8
                ),
                'N/A'
            ) as supplier_delivery_note,
            COALESCE(v.description, 'N/A') as vendor_name,
            COALESCE(brh.contact_email, '') as contact_email,
            CAST(SUM(IFNULL(pi2.qty_loose, 0)) AS UNSIGNED) as total_po_ordered_pcs,
            SUM(IFNULL(pi2.item_nett_amt, 0)) as total_po_netamt
        FROM po p
        LEFT JOIN branch brh ON brh.id = p.po_branch_id
        INNER JOIN po_items pi2 ON p.id = pi2.po_id
        LEFT JOIN vendor v ON v.id = p.vendor_id
        """
        
        where_clauses = ["p.hq_po_id = %s"]
        params = [search_hq_po_id]

        # Apply branch filters using the PO checker pattern
        where_clauses, params = _apply_branch_filters_to_po_notification_query(where_clauses, params, user_context)

        full_where_clause = " AND ".join(where_clauses)
        
        group_by_clause = """
        GROUP BY p.po_no, p.po_date, p.hq_po_id, brh.code, brh.description, 
                 p.delivered, p.delivery_date, p.remark, v.description, brh.contact_email
        ORDER BY p.po_no
        """
        
        query = f"{select_clause} WHERE {full_where_clause} {group_by_clause}"
        
        cursor = db.cursor(dictionary=True)
        cursor.execute(query, tuple(params))
        results = cursor.fetchall()

        if not results:
            logger.info(f"No PO data found for HQ PO ID: {hq_po_id}")
            return {}

        logger.info(f"Found {len(results)} PO records for HQ PO ID: {hq_po_id}")

        # Process results into summary and detail lists
        po_data = []
        master_po_total_qty = 0
        master_po_total_amount = 0
        
        for row in results:
            # Convert byte arrays to strings if necessary
            for key, value in row.items():
                if isinstance(value, bytes):
                    row[key] = value.decode('utf-8')

            # Convert hq_po_id from int to string for Pydantic validation
            if 'hq_po_id' in row and row['hq_po_id'] is not None:
                row['hq_po_id'] = str(row['hq_po_id'])

            # Ensure po_no is not None and convert to string
            if 'po_no' in row:
                if row['po_no'] is None:
                    row['po_no'] = 'N/A'
                else:
                    row['po_no'] = str(row['po_no'])

            # Ensure other critical fields have default values
            if 'po_branch_code' in row and row['po_branch_code'] is None:
                row['po_branch_code'] = 'N/A'
            if 'po_branch_name' in row and row['po_branch_name'] is None:
                row['po_branch_name'] = 'N/A'
            if 'vendor_name' in row and row['vendor_name'] is None:
                row['vendor_name'] = 'N/A'
            if 'contact_email' in row and row['contact_email'] is None:
                row['contact_email'] = ''

            # Pydantic validator will handle quantity conversion to integer

            try:
                po_data.append(PONotificationItem(**row)) # Use the Pydantic model
            except Exception as e:
                logger.error(f"Error creating PONotificationItem from row data: {row}")
                logger.error(f"Pydantic validation error: {str(e)}")
                # Skip this row and continue with others
                continue
            
            # Aggregate totals for the summary
            qty = row.get('total_po_ordered_pcs', 0)
            if isinstance(qty, (int, float, Decimal)):
                master_po_total_qty += qty
                
            amount = row.get('total_po_netamt', 0)
            if isinstance(amount, (int, float, Decimal)):
                master_po_total_amount += amount

        # Create summary object
        summary = {
            "hq_po_id": hq_po_id,
            "po_date": results[0]['po_date'] if results else None,
            "vendor_name": results[0]['vendor_name'] if results else None,
            "master_po_total_qty": master_po_total_qty,
            "master_po_total_amount": master_po_total_amount,
        }
        
        # Apply cost permission to summary
        if not user_context.get('has_cost_permission', False):
            summary['master_po_total_amount'] = "[No View Permission]"
            
        # Apply cost permission to details
        if not user_context.get('has_cost_permission', False):
            for item in po_data:
                item.total_po_netamt = "[No View Permission]"

        return {"summary": summary, "po_data": po_data}

    except mysql.connector.Error as err:
        logger.error(f"MySQL Error in get_pos_by_hq_po_id: {err}")
        return {}
    finally:
        if cursor:
            cursor.close()

def get_bulk_pos_by_hq_po_ids(db: mysql.connector.MySQLConnection, hq_po_ids: List[str], user_context: Dict[str, Any]) -> Dict[str, Any]:
    """
    Search for multiple POs by HQ PO IDs (Master PO Nos.)

    Args:
        db: MySQL database connection
        hq_po_ids: List of HQ PO IDs (Master PO Nos.) to search for
        user_context: User context for permissions

    Returns:
        A dictionary containing results for each HQ PO ID
    """
    results = {}
    total_found = 0

    for hq_po_id in hq_po_ids:
        try:
            result = get_pos_by_hq_po_id(db, hq_po_id, user_context)
            if result and result.get('po_data'):
                results[hq_po_id] = {
                    "success": True,
                    "message": f"Found {len(result['po_data'])} PO(s) for {hq_po_id}",
                    "hq_po_id": hq_po_id,
                    "summary": result.get('summary'),
                    "po_data": result.get('po_data')
                }
                total_found += 1
            else:
                results[hq_po_id] = {
                    "success": False,
                    "message": f"No POs found for {hq_po_id}",
                    "hq_po_id": hq_po_id,
                    "summary": None,
                    "po_data": []
                }
        except Exception as e:
            logger.error(f"Error searching for HQ PO ID {hq_po_id}: {str(e)}")
            results[hq_po_id] = {
                "success": False,
                "message": f"Error searching for {hq_po_id}: {str(e)}",
                "hq_po_id": hq_po_id,
                "summary": None,
                "po_data": []
            }

    return {
        "success": total_found > 0,
        "message": f"Found {total_found} Master PO(s) out of {len(hq_po_ids)} searched",
        "results": results,
        "total_found": total_found,
        "total_searched": len(hq_po_ids)
    }

async def send_bulk_po_notification_email(
    hq_po_ids: List[str],
    bulk_results: Dict[str, Any],
    user_context: Dict[str, Any],
    cc_emails: Optional[List[str]] = None,
    override_email: Optional[List[str]] = None,
    custom_subject: Optional[str] = None,
    custom_message: Optional[str] = None
) -> Dict[str, Any]:
    """
    Send bulk email notifications for multiple Master POs.
    """
    try:
        total_emails_sent = 0
        total_pos_processed = 0
        results = {}

        for hq_po_id in hq_po_ids:
            if hq_po_id in bulk_results.get('results', {}):
                po_result = bulk_results['results'][hq_po_id]
                if po_result.get('success') and po_result.get('po_data'):
                    # Convert dict back to PONotificationItem objects
                    from ..models import PONotificationItem
                    po_data = [PONotificationItem(**item) for item in po_result['po_data']]

                    # Send email for this Master PO
                    email_result = await send_po_notification_email(
                        po_data=po_data,
                        hq_po_id=hq_po_id,
                        user_context=user_context,
                        cc_emails=cc_emails,
                        override_email=override_email,
                        summary=po_result.get('summary'),
                        custom_subject=custom_subject,
                        custom_message=custom_message
                    )

                    results[hq_po_id] = email_result
                    if email_result.get('success'):
                        total_emails_sent += 1
                        total_pos_processed += email_result.get('po_count', 0)
                else:
                    results[hq_po_id] = {
                        'success': False,
                        'message': f'No PO data found for {hq_po_id}',
                        'po_count': 0,
                        'po_numbers': []
                    }
            else:
                results[hq_po_id] = {
                    'success': False,
                    'message': f'Master PO {hq_po_id} not found in search results',
                    'po_count': 0,
                    'po_numbers': []
                }

        return {
            'success': total_emails_sent > 0,
            'message': f'Successfully sent emails for {total_emails_sent} Master PO(s), processed {total_pos_processed} individual PO(s)',
            'total_emails_sent': total_emails_sent,
            'total_pos_processed': total_pos_processed,
            'results': results
        }

    except Exception as e:
        logger.error(f"Error sending bulk PO notification emails: {str(e)}")
        return {
            'success': False,
            'message': f'Bulk email sending failed: {str(e)}',
            'total_emails_sent': 0,
            'total_pos_processed': 0,
            'results': {}
        }

async def send_po_notification_email(
    po_data: List[PONotificationItem],
    hq_po_id: str,
    user_context: Dict[str, Any],
    cc_emails: Optional[List[str]] = None,
    override_email: Optional[List[str]] = None,
    summary: Optional[Dict[str, Any]] = None,
    custom_subject: Optional[str] = None,
    custom_message: Optional[str] = None
) -> Dict[str, Any]:
    """
    Send email notification for PO creation.
    - If override_email is provided, sends one email to that address.
    - Otherwise, sends individual emails to each branch's contact_email.
    - cc_emails are added to all outgoing emails.
    """
    try:
        from ..services.mail_service import mail_service
        from ..services.email_log_service import email_log_service
        from collections import defaultdict

        # Create a default summary if not provided
        if not summary:
            summary = {
                "hq_po_id": hq_po_id,
                "po_date": po_data[0].po_date if po_data else None,
                "vendor_name": "N/A",  # This would need to be passed from the calling function
                "master_po_total_qty": sum(getattr(p, 'total_po_ordered_pcs', 0) or 0 for p in po_data),
                "master_po_total_amount": "N/A"  # This would need cost permission handling
            }

        if override_email:
            # Send a single email to each override address
            subject = custom_subject or f"PO Notification - HQ PO ID: {hq_po_id}"
            html_details = render_po_details_html(po_data, summary)

            from jinja2 import Environment, FileSystemLoader
            import os

            template_dir = os.path.join(os.path.dirname(__file__), '..', 'services', 'email_templates')
            env = Environment(loader=FileSystemLoader(template_dir))
            template = env.get_template('notification.html')

            # Use custom message if provided, otherwise use default message
            email_message = custom_message or f"Please find the details for {len(po_data)} Purchase Order(s) related to HQ PO ID: {hq_po_id}."

            full_html_content = template.render(
                title=subject,
                message=email_message,
                details_html=html_details,
                action_url=None,
                current_year=datetime.now().year
            )
            
            for email in override_email:
                try:
                    await mail_service.send_email(
                        to_email=email,
                        subject=subject,
                        html_content=full_html_content,
                        cc_emails=cc_emails
                    )
                    await email_log_service.log_email(
                        recipient=email,
                        subject=subject,
                        status='sent',
                        cc_recipients=cc_emails,
                        body=full_html_content,
                        source='po_notification_override',
                        reference_id=hq_po_id
                    )
                except Exception as e:
                    await email_log_service.log_email(
                        recipient=email,
                        subject=subject,
                        status='failed',
                        cc_recipients=cc_emails,
                        body=full_html_content,
                        error_message=str(e),
                        source='po_notification_override',
                        reference_id=hq_po_id
                    )
            
            po_numbers = [po.po_no for po in po_data if po.po_no]
            return {
                'success': True, 
                'message': f'Email sent to {", ".join(override_email)}',
                'po_count': len(po_data),
                'po_numbers': po_numbers
            }
        else:
            # Group POs by branch email
            grouped_pos = defaultdict(list)
            for po in po_data:
                if po.contact_email:
                    grouped_pos[po.contact_email].append(po)

            sent_count = 0
            po_numbers = []

            for email, pos in grouped_pos.items():
                subject = custom_subject or f"PO Notification - {len(pos)} New Purchase Order(s)"

                # Create branch-specific summary
                branch_summary = {
                    "hq_po_id": hq_po_id,
                    "po_date": summary.get('po_date', 'N/A'),
                    "vendor_name": summary.get('vendor_name', 'N/A'),
                    "master_po_total_qty": sum(getattr(p, 'total_po_ordered_pcs', 0) or 0 for p in pos),
                    "master_po_total_amount": summary.get('master_po_total_amount', 'N/A')
                }

                html_details = render_po_details_html(pos, branch_summary)

                from jinja2 import Environment, FileSystemLoader
                import os

                template_dir = os.path.join(os.path.dirname(__file__), '..', 'services', 'email_templates')
                env = Environment(loader=FileSystemLoader(template_dir))
                template = env.get_template('notification.html')

                # Use custom message if provided, otherwise use default message
                email_message = custom_message or f"Please find details for {len(pos)} new Purchase Order(s) assigned to your branch."

                full_html_content = template.render(
                    title=subject,
                    message=email_message,
                    details_html=html_details,
                    action_url=None,
                    current_year=datetime.now().year
                )
                
                try:
                    await mail_service.send_email( # Corrected from mail_service.send_ to mail_service.send_email
                        to_email=email,
                        subject=subject,
                        html_content=full_html_content,
                        cc_emails=cc_emails
                    )
                    sent_count += 1
                    po_numbers.extend([p.po_no for p in pos if p.po_no])
                    
                    await email_log_service.log_email(
                        recipient=email,
                        subject=subject,
                        status='sent',
                        cc_recipients=cc_emails,
                        body=full_html_content,
                        source='po_notification_branch',
                        reference_id=hq_po_id
                    )
                except Exception as e:
                    await email_log_service.log_email(
                        recipient=email,
                        subject=subject,
                        status='failed',
                        cc_recipients=cc_emails,
                        body=full_html_content,
                        error_message=str(e),
                        source='po_notification_branch',
                        reference_id=hq_po_id
                    )

            if sent_count > 0:
                return {
                    "success": True,
                    "message": f"Notifications sent for {sent_count} branch(es).",
                    'po_count': len(po_data),
                    'po_numbers': po_numbers
                }
            else:
                return {
                    "success": True,
                    "message": "No emails to send. Check if branches have contact emails.",
                    'po_count': len(po_data),
                    'po_numbers': []
                }

    except Exception as e:
        logger.error(f"Error sending PO notification email: {str(e)}")
        return {'success': False, 'message': str(e)}

def render_po_details_html(po_data: List[PONotificationItem], summary: Dict[str, Any]) -> str:
    """Helper to render PO details table for email with master PO information."""
    
    # Format quantity as whole number
    def format_quantity(qty):
        if qty is None:
            return '0'
        if isinstance(qty, str):
            return qty
        try:
            return f"{int(float(qty)):,}"
        except (ValueError, TypeError):
            return '0'
    
    # Master PO Information Section - Using table layout for Outlook compatibility
    master_po_info = f"""
    <div class="master-po-section">
        <h3 style="color: #1890ff; margin-bottom: 15px; font-size: 18px;">Master PO Information</h3>
        <table style="width: 100%; background-color: #f8f9fa; border-radius: 6px; margin-bottom: 25px; border-collapse: collapse;">
            <tr>
                <td style="padding: 20px; width: 25%; vertical-align: top;">
                    <strong style="color: #333;">Master PO No:</strong><br>
                    <span style="color: #555;">{summary.get('hq_po_id', 'N/A')}</span>
                </td>
                <td style="padding: 20px; width: 25%; vertical-align: top;">
                    <strong style="color: #333;">PO Date:</strong><br>
                    <span style="color: #555;">{summary.get('po_date', 'N/A')}</span>
                </td>
                <td style="padding: 20px; width: 25%; vertical-align: top;">
                    <strong style="color: #333;">Vendor:</strong><br>
                    <span style="color: #555;">{summary.get('vendor_name', 'N/A')}</span>
                </td>
                <td style="padding: 20px; width: 25%; vertical-align: top;">
                    <strong style="color: #333;">Total Quantity:</strong><br>
                    <span style="color: #555; font-weight: bold;">{format_quantity(summary.get('master_po_total_qty', 'N/A'))}</span>
                </td>
            </tr>
        </table>
    </div>
    """
    
    # PO Details Table Section - Enhanced with responsive wrapper and better styling
    po_details_table = f"""
    <div class="po-details-section">
        <h3 style="color: #1890ff; margin-bottom: 15px; font-size: 18px;">Purchase Order Details</h3>
        <div style="overflow-x: auto; margin: 20px 0; border: 1px solid #e9ecef; border-radius: 6px;">
            <table style="width: 100%; min-width: 800px; border-collapse: collapse; font-size: 13px; background-color: #fff;">
                <thead>
                    <tr style="background-color: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                        <th style="border: 1px solid #dee2e6; padding: 12px 8px; text-align: left; font-weight: 600; color: #495057; white-space: nowrap;">PO No</th>
                        <th style="border: 1px solid #dee2e6; padding: 12px 8px; text-align: left; font-weight: 600; color: #495057; white-space: nowrap;">Branch Code</th>
                        <th style="border: 1px solid #dee2e6; padding: 12px 8px; text-align: left; font-weight: 600; color: #495057; white-space: nowrap;">Branch Name</th>
                        <th style="border: 1px solid #dee2e6; padding: 12px 8px; text-align: left; font-weight: 600; color: #495057; white-space: nowrap;">Delivery Date</th>
                        <th style="border: 1px solid #dee2e6; padding: 12px 8px; text-align: left; font-weight: 600; color: #495057; white-space: nowrap;">Supplier Delivery Note</th>
                        <th style="border: 1px solid #dee2e6; padding: 12px 8px; text-align: right; font-weight: 600; color: #495057; white-space: nowrap;">Total Ordered Pcs</th>
                    </tr>
                </thead>
                <tbody>
    """
    
    for i, po in enumerate(po_data):
        # Alternate row colors for better readability
        row_bg = "#f8f9fa" if i % 2 == 0 else "#ffffff"
        
        po_details_table += f"""
                    <tr style="background-color: {row_bg};">
                        <td style="border: 1px solid #dee2e6; padding: 10px 8px; word-wrap: break-word; max-width: 120px; overflow-wrap: break-word;">{po.po_no}</td>
                        <td style="border: 1px solid #dee2e6; padding: 10px 8px; word-wrap: break-word; max-width: 80px; overflow-wrap: break-word;">{po.po_branch_code or 'N/A'}</td>
                        <td style="border: 1px solid #dee2e6; padding: 10px 8px; word-wrap: break-word; max-width: 180px; overflow-wrap: break-word;">{po.po_branch_name or 'N/A'}</td>
                        <td style="border: 1px solid #dee2e6; padding: 10px 8px; word-wrap: break-word; max-width: 100px; overflow-wrap: break-word;">{po.delivery_date or 'N/A'}</td>
                        <td style="border: 1px solid #dee2e6; padding: 10px 8px; word-wrap: break-word; max-width: 200px; overflow-wrap: break-word;">{po.supplier_delivery_note or 'N/A'}</td>
                        <td style="border: 1px solid #dee2e6; padding: 10px 8px; text-align: right; word-wrap: break-word; max-width: 100px; overflow-wrap: break-word; font-weight: 600; color: #28a745;">{format_quantity(po.total_po_ordered_pcs)}</td>
                    </tr>
        """
    
    po_details_table += """
                </tbody>
            </table>
        </div>
        <p style="font-size: 12px; color: #6c757d; margin-top: 10px; font-style: italic;">
            💡 Tip: If the table appears cut off, try scrolling horizontally or viewing in a larger screen.
        </p>
    </div>
    """
    
    return master_po_info + po_details_table


