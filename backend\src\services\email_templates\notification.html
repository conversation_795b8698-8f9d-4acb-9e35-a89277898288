<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        /* Outlook-specific styles */
        table {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
            border-collapse: collapse;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            width: 100% !important;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
            margin-bottom: 20px;
        }

        .content {
            padding: 0 20px;
        }
        .title {
            font-size: 20px;
            margin-bottom: 20px;
            color: #333;
            font-weight: bold;
        }
        .message {
            font-size: 16px;
            line-height: 1.8;
            margin-bottom: 30px;
            color: #555;
            white-space: pre-line;
        }
        .action-button {
            display: inline-block;
            background-color: #1890ff;
            color: white !important;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            transition: background-color 0.3s;
        }
        .action-button:hover {
            background-color: #0d7cd6;
        }
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            text-align: center;
            font-size: 14px;
            color: #888;
        }
        
        /* Enhanced responsive design for tables */
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            margin: 20px 0;
        }
        
        .details-section {
            margin: 30px 0;
        }
        
        .details-section h3 {
            color: #1890ff;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        /* Table styling improvements */
        .po-details-section table {
            font-size: 13px;
            width: 100%;
            min-width: 1000px;
        }
        
        .po-details-section th {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            color: #495057;
            white-space: nowrap;
        }
        
        .po-details-section td {
            border: 1px solid #dee2e6;
            padding: 10px 8px;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
        
        /* Mobile responsive adjustments */
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
                max-width: calc(100% - 20px);
            }
            .content {
                padding: 0 10px;
            }
            .action-button {
                display: block;
                width: 100%;
                box-sizing: border-box;
            }
            .po-details-section table {
                font-size: 12px;
            }
            .po-details-section th,
            .po-details-section td {
                padding: 8px 6px;
            }
        }
        
        /* Print styles */
        @media print {
            .container {
                box-shadow: none;
                margin: 0;
                padding: 20px;
            }
            .table-responsive {
                overflow: visible;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="content">
            <div class="title">
                {{ title }}
            </div>
            
            <div class="message">
                {{ message }}
            </div>

            {% if details_html %}
            <div class="details-section">
                {{ details_html|safe }}
            </div>
            {% endif %}
            
            {% if action_url %}
            <div class="button-container">
                <a href="{{ action_url }}" class="action-button">Take Action</a>
            </div>
            {% endif %}
        </div>
        
        <div class="footer">
            <p>
                This email was automatically generated.<br>
                Please do not reply to this email.
            </p>
        </div>
    </div>
</body>
</html> 