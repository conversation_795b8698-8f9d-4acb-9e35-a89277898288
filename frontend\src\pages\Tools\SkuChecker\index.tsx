import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Page<PERSON>ontainer,
  ProCard,
  ProFormRadio,
  ProTable,
} from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-components';
import {
  Button,
  Descriptions,
  Typography,
  Alert,
  Space,
  Spin,
  Tag,
  Empty,
  Input,
  Checkbox,
  Row,
  Col,
  message,
  ConfigProvider,
  Modal,
  Radio,
} from 'antd';
import { fetchSkuDetails, exportSkuChecker, fetchSkuSalesSummary } from '@/services/tools';
import type { SkuDetailItem, FetchSkuDetailsParams, SkuCheckerExportRequest, SkuSalesSummaryResponse } from '@/services/tools';
import ExportHistory from '@/components/ExportHistory';
import SkuImageGallery from '@/components/SkuImageGallery';
import QOHModal from '@/components/QOHModal';
import { DownloadOutlined, CameraOutlined, PictureOutlined, SearchOutlined, QrcodeOutlined, ShopOutlined, BarChartOutlined } from '@ant-design/icons';
import enUS from 'antd/locale/en_US';
import { useModel } from '@umijs/max';
import Quagga from '@ericblade/quagga2'; // ES6
import { useHasCostPermission, useHasManageSkuImagePermission } from '@/utils/permissions';
import JsBarcode from 'jsbarcode';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

const { Text } = Typography;

type SearchType = 'sku_item_code' | 'artno' | 'mcode' | 'description';
type SearchScope = 'parent' | 'all';

/**
 * Highlights occurrences of a search term within a given text.
 *
 * @param textInput The text to search within. Can be any type, will be converted to string.
 * @param highlight The search term to highlight. Defaults to an empty string.
 * @returns A React.ReactNode with the search term highlighted.
 */
const getHighlightedText = (textInput: any, highlight: string = '') => {
  const text = String(textInput || ''); // Explicitly convert to string
  if (!highlight.trim()) {
    return <span>{text}</span>;
  }
  // Ensure highlight is also treated as a string for the regex
  const highlightStr = String(highlight || '');
  const parts = text.split(new RegExp(`(${highlightStr.replace(/[.*+?^${}\\'()'|[\]\\]/g, '\\\\$&')})`, 'gi'));
  return (
    <span>
      {parts.map((part, i) =>
        part.toLowerCase() === highlight.toLowerCase() ? (
          <span key={i} style={{ backgroundColor: '#ffc069', padding: 0 }}>
            {part}
          </span>
        ) : (
          part
        ),
      )}
    </span>
  );
};

/**
 * @module SkuCheckerPage
 * @description
 * The SkuCheckerPage component provides a user interface for searching SKU (Stock Keeping Unit)
 * details from a backend API. It allows users to search by various criteria such as ARMSCode,
 * ArtNo, MCode, or Description, with options for wildcard search and search scope
 * (parent codes only or all codes).
 *
 * Features:
 * - Search form with selectable search type, search scope, and wildcard option.
 * - Display of search results in a ProTable with client-side sorting and pagination.
 * - Highlighting of the search term in the results table.
 * - An "instant search" box within the table's toolbar to filter displayed results.
 * - Detailed view of a selected SKU using Ant Design Descriptions.
 * - Loading states, error handling, and empty state display.
 */
const SkuCheckerPage: React.FC = () => {
  // Component State
  // ---------------
  // Loading and Error States
  const [loading, setLoading] = useState<boolean>(false); // True when an API call is in progress
  const [error, setError] = useState<string | null>(null); // Stores error messages from API calls or validation

  // Search Results and Selection
  const [searchResults, setSearchResults] = useState<SkuDetailItem[]>([]); // Stores the raw results from the API
  const [selectedSku, setSelectedSku] = useState<SkuDetailItem | null>(null); // Stores the currently selected SKU from the table
  const [apiTotalResults, setApiTotalResults] = useState<number>(0); // Total number of items available from the backend for the current query
  
  // Search Form Input States
  const [searchType, setSearchType] = useState<SearchType>('artno'); // Default to Art No
  const [searchValue, setSearchValue] = useState<string>(''); // Current value in the main search input field
  const [searchScope, setSearchScope] = useState<SearchScope>('parent'); // Search scope ('parent' or 'all')
    const [useWildcard, setUseWildcard] = useState<boolean>(false); // Whether wildcard search is enabled for code fields
  const [searchScopeDisabled, setSearchScopeDisabled] = useState<boolean>(false); // To control search scope input

  // Pagination State
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [currentPageSize, setCurrentPageSize] = useState<number>(10);

  // Search State Management
  const [searchPerformed, setSearchPerformed] = useState<boolean>(false); // True if a search has been attempted, to differentiate initial state from "no results"
  const [currentMainSearchValue, setCurrentMainSearchValue] = useState<string>(''); // Stores the searchValue that was used for the most recent API call (for highlighting)
  const [currentMainSearchType, setCurrentMainSearchType] = useState<SearchType>('artno'); // Stores the searchType for the most recent API call (for highlighting)

  // Table Interaction States
  const [tableSearchText, setTableSearchText] = useState<string>(''); // Text used for filtering results directly within the table

  // Scanner state
  const [isScannerModalVisible, setIsScannerModalVisible] = useState<boolean>(false);
  const [scannerInitializing, setScannerInitializing] = useState<boolean>(false);
  const [scannerError, setScannerError] = useState<string | null>(null);
  const [triggerSearch, setTriggerSearch] = useState<boolean>(false);
  const interactiveRef = useRef<HTMLDivElement>(null);
  const quaggaInitializedRef = useRef<boolean>(false);

  // Image gallery state
  const [imageGalleryVisible, setImageGalleryVisible] = useState<boolean>(false);
  const [currentArtno, setCurrentArtno] = useState<string>('');

  // Large result set modal state
  const [isLargeResultModalVisible, setIsLargeResultModalVisible] = useState<boolean>(false);
  const [largeResultMessage, setLargeResultMessage] = useState<string>('');
  const [currentSearchParams, setCurrentSearchParams] = useState<FetchSkuDetailsParams | null>(null);

  // Barcode generator state
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<SkuDetailItem[]>([]);
  const [barcodeModalVisible, setBarcodeModalVisible] = useState<boolean>(false);
  const [generatingPDF, setGeneratingPDF] = useState<boolean>(false);
  const barcodePreviewRef = useRef<HTMLDivElement>(null);

  // QOH modal state
  const [qohModalVisible, setQohModalVisible] = useState<boolean>(false);
  const [qohSelectedSku, setQohSelectedSku] = useState<SkuDetailItem | null>(null);

  // Sales summary modal state
  const [salesModalVisible, setSalesModalVisible] = useState<boolean>(false);
  const [salesLoading, setSalesLoading] = useState<boolean>(false);
  const [salesData, setSalesData] = useState<SkuSalesSummaryResponse | null>(null);
  const [salesSelectedSku, setSalesSelectedSku] = useState<SkuDetailItem | null>(null);
  const [salesSearchText, setSalesSearchText] = useState<string>('');

  const searchTypeLabels: Record<SearchType, string> = {
    sku_item_code: 'ARMSCode',
    artno: 'Art No',
    mcode: 'MCode',
    description: 'Description',
  };
  
  /**
   * Checks if the current searchType is one of the code-based search types (ARMSCode, ArtNo, MCode).
   * @returns True if the search type is code-based, false otherwise.
   */
  const isCodeSearchType = (): boolean => {
    return ['sku_item_code', 'artno', 'mcode'].includes(searchType);
  };

  const getSearchPlaceholder = () => {
    const basePlaceholder = `Enter ${searchTypeLabels[searchType]}`;
    if (isCodeSearchType()) {
      return `${basePlaceholder}(s), one per line`;
    }
    return basePlaceholder;
  };

  /**
   * Handles the main search submission.
   * Fetches ALL SKU details from the API based on the current search form values.
   * Updates state with results, loading status, and errors.
   * Now uses client-side pagination for better user experience.
   */
  const handleSearch = async () => {
    if (!searchValue.trim()) {
      setError('Please enter a search value.');
      setSearchResults([]);
      setSelectedSku(null);
      setSearchPerformed(true);
      setCurrentMainSearchValue('');
      return;
    }
    setLoading(true);
    setError(null);
    setSearchPerformed(true);
    setCurrentMainSearchValue(searchValue); // Store the search value used
    setCurrentMainSearchType(searchType); // Store the search type used

    // Fetch ALL results (no pagination) for client-side pagination
    const params: FetchSkuDetailsParams = {
      page_size: 10000, // Large number to get all results
      page: 1,
      search_scope: searchScope,
    };

    if (isCodeSearchType()) {
      params[searchType] = searchValue;
      params.use_wildcard = useWildcard;
    } else {
      params.description = searchValue;
    }

    try {
      const response = await fetchSkuDetails(params);

      if (response.success && response.data) {
        setSearchResults(response.data);
        setApiTotalResults(response.grand_total); // Store total for reference
        setCurrentPage(1); // Reset to first page

        // Check if results are limited due to large result set
        if (response.is_result_limited && response.export_recommended) {
          setLargeResultMessage(response.result_limit_message || 'Large result set detected');
          setCurrentSearchParams(params); // Store search params for export
          setIsLargeResultModalVisible(true);
        }

        if (response.data.length > 0) {
          console.log("First item in searchResults:", JSON.stringify(response.data[0], null, 2));
        }

        if (response.data.length > 0) {
          setSelectedSku(response.data[0]);
          if (response.data.length === 1 && error && error.startsWith("Multiple items found")) {
            setError(null);
          }
        } else {
          setError('No SKU found matching your criteria.');
        }
      } else {
        setError(response.error || 'Failed to fetch SKU details.');
        setApiTotalResults(0);
      }
    } catch (e: any) {
      console.error("Search error:", e);
      setError(e.message || 'An unexpected error occurred.');
      setApiTotalResults(0);
    }
    setLoading(false);
  };

  /**
   * Resets the search form and results to their initial states.
   */
  const handleReset = () => {
    setSearchValue('');
    setSearchResults([]);
    setSelectedSku(null);
    setApiTotalResults(0);
    setCurrentPage(1);
    setCurrentPageSize(10);
    setError(null);
    setSearchPerformed(false);
    setCurrentMainSearchValue('');
    setTableSearchText('');
    setIsLargeResultModalVisible(false);
    setLargeResultMessage('');
    setCurrentSearchParams(null);
    // Clear barcode generator selections
    setSelectedRowKeys([]);
    setSelectedRows([]);
    setBarcodeModalVisible(false);
  };

  // Effect for search type changes - update search scope and control availability
  useEffect(() => {
    if (searchType === 'sku_item_code' || searchType === 'mcode') {
      // For these types, 'all' is mandatory.
      // Set it if it's not already 'all' to ensure consistency and disable the control.
      if (searchScope !== 'all') {
          setSearchScope('all');
      }
      setSearchScopeDisabled(true);
    } else { // 'artno' or 'description'
      // For these types, the user can choose scope. Enable the control.
      // Do NOT change searchScope here automatically; let the user's selection persist.
      setSearchScopeDisabled(false);
    }
  }, [searchType, searchScope]); // Added searchScope as a dependency

  // Removed useEffect for pagination changes to enable client-side pagination
  // Now all search results are fetched once and pagination happens on the frontend

  const columns: ProColumns<SkuDetailItem>[] = [
    {
      title: 'Actions',
      key: 'actions',
      width: 140,
      fixed: 'left',
      render: (text_ignored, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<ShopOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              handleOpenQOHModal(record);
            }}
            title="Check QOH (Quantity On Hand)"
            style={{ padding: '0 4px' }}
          >
            QOH
          </Button>
          <Button
            type="link"
            size="small"
            icon={<BarChartOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              handleOpenSalesModal(record);
            }}
            title="View Sales Summary"
            style={{ padding: '0 4px' }}
          >
            Sales
          </Button>
        </Space>
      ),
    },
    { 
      title: 'ARMS Code', 
      dataIndex: 'sku_item_code', 
      key: 'sku_item_code', 
      width: 120, 
      ellipsis: true,
      responsive: ['lg'],
      sorter: (a, b) => (String(a.sku_item_code || '')).localeCompare(String(b.sku_item_code || '')),
      render: (text_ignored, record, index) => {
        const cellValue = record.sku_item_code;
        if (index === 0) { 
          console.log(`ARMSCode render - cellValue from record:`, cellValue, `original text arg:`, text_ignored);
        }
        const displayValue = String(cellValue === null || cellValue === undefined ? '' : cellValue);
        return currentMainSearchType === 'sku_item_code' ? getHighlightedText(displayValue, currentMainSearchValue) : displayValue;
      }
    },
    { 
      title: 'Art No', 
      dataIndex: 'Art_No', 
      key: 'Art_No', 
      width: 120, 
      ellipsis: true,
      sorter: (a, b) => (String(a.Art_No || '')).localeCompare(String(b.Art_No || '')),
      defaultSortOrder: 'ascend',
      render: (text_ignored, record, index) => {
        const cellValue = record.Art_No;
        if (index === 0) { 
          console.log(`Art_No render - cellValue from record:`, cellValue, `original text arg:`, text_ignored);
        }
        const displayValue = String(cellValue === null || cellValue === undefined ? '' : cellValue);
        return currentMainSearchType === 'artno' ? getHighlightedText(displayValue, currentMainSearchValue) : displayValue;
      }
    },
    { 
      title: 'MCode', 
      dataIndex: 'MCode', 
      key: 'MCode', 
      width: 100, 
      ellipsis: true,
      responsive: ['md'],
      sorter: (a, b) => (String(a.MCode || '')).localeCompare(String(b.MCode || '')),
      render: (text_ignored, record) => {
        const cellValue = record.MCode;
        const displayValue = String(cellValue === null || cellValue === undefined ? '' : cellValue);
        return currentMainSearchType === 'mcode' ? getHighlightedText(displayValue, currentMainSearchValue) : displayValue;
      }
    },
    { 
      title: 'Description', 
      dataIndex: 'ART_NO DESCRIPTION', 
      key: 'ART_NO DESCRIPTION', 
      ellipsis: true,
      responsive: ['sm'],
      sorter: (a, b) => (String(a['ART_NO DESCRIPTION'] || '')).localeCompare(String(b['ART_NO DESCRIPTION'] || '')),
      render: (text_ignored, record) => {
        const cellValue = record['ART_NO DESCRIPTION'];
        const displayValue = String(cellValue === null || cellValue === undefined ? '' : cellValue); 
        return currentMainSearchType === 'description' ? getHighlightedText(displayValue, currentMainSearchValue) : displayValue;
      }
    },
    { 
      title: 'Brand', 
      dataIndex: 'Brand', 
      key: 'Brand', 
      width: 100, 
      ellipsis: true,
      responsive: ['md'],
      sorter: (a, b) => (String(a.Brand || '')).localeCompare(String(b.Brand || '')),
      render: (text_ignored, record) => String(record.Brand === null || record.Brand === undefined ? 'N/A' : record.Brand)
    },
    { 
      title: 'Color', 
      dataIndex: 'Color', 
      key: 'Color', 
      width: 80, 
      ellipsis: true,
      responsive: ['lg'],
      sorter: (a, b) => (String(a.Color || '')).localeCompare(String(b.Color || '')),
      render: (text_ignored, record) => String(record.Color === null || record.Color === undefined ? 'N/A' : record.Color)
    },
    { 
      title: 'Size', 
      dataIndex: 'Size', 
      key: 'Size', 
      width: 70, 
      ellipsis: true,
      sorter: (a, b) => (String(a.Size || '')).localeCompare(String(b.Size || '')),
      render: (text_ignored, record) => String(record.Size === null || record.Size === undefined ? 'N/A' : record.Size)
    },
    { 
      title: 'RSP', 
      dataIndex: 'RSP', 
      key: 'RSP', 
      width: 90, 
      ellipsis: true,
      sorter: (a, b) => (a.RSP || 0) - (b.RSP || 0),
      render: (text_ignored, record) => {
        const rsp = record.RSP;
        return rsp ? `RM ${rsp.toFixed(2)}` : 'N/A';
      }
    },
    { 
      title: 'RSP Disc', 
      dataIndex: 'RSP_Discount', 
      key: 'RSP_Discount', 
      width: 90, 
      ellipsis: true,
      responsive: ['md'],
      sorter: (a, b) => (String(a.RSP_Discount || '')).localeCompare(String(b.RSP_Discount || '')),
      render: (text_ignored, record) => {
        const discount = record.RSP_Discount;
        return (
          <Tag color="orange" style={{ fontWeight: 'bold', fontSize: '11px' }}>
            {discount || 'N/A'}
          </Tag>
        );
      }
    },
    { 
      title: 'Net Price', 
      dataIndex: 'Net_Selling_Price', 
      key: 'Net_Selling_Price', 
      width: 100, 
      ellipsis: true,
      sorter: (a, b) => (a.Net_Selling_Price || 0) - (b.Net_Selling_Price || 0),
      render: (text_ignored, record) => {
        const netPrice = record.Net_Selling_Price;
        return (
          <Tag color="green" style={{ fontWeight: 'bold', fontSize: '11px' }}>
            {netPrice ? `RM ${netPrice.toFixed(2)}` : 'N/A'}
          </Tag>
        );
      }
    },
    { 
      title: 'Season', 
      dataIndex: 'Season_Code', 
      key: 'Season_Code', 
      width: 80, 
      ellipsis: true,
      responsive: ['lg'],
      sorter: (a, b) => (String(a.Season_Code || '')).localeCompare(String(b.Season_Code || '')),
      render: (text_ignored, record) => String(record.Season_Code === null || record.Season_Code === undefined ? 'N/A' : record.Season_Code)
    },
    {
      title: 'Status',
      dataIndex: 'Active',
      key: 'Active',
      width: 80,
      responsive: ['sm'],
      sorter: (a, b) => (String(a.Active || '')).localeCompare(String(b.Active || '')),
      render: (text_ignored, record) => {
        const activeStr = String(record.Active === null || record.Active === undefined ? 'Error' : record.Active);
        return (
          <Tag color={activeStr === 'Active' ? 'green' : activeStr === 'Inactive' ? 'red' : 'orange'} style={{ fontSize: '11px' }}>
            {activeStr}
          </Tag>
        );
      },
    },
    {
      title: 'Parent',
      dataIndex: 'is_parent',
      key: 'is_parent',
      width: 70,
      responsive: ['lg'],
      sorter: (a, b) => (a.is_parent || '').localeCompare(b.is_parent || ''),
      render: (text_ignored, record) => {
        const isParent = record.is_parent;
        return isParent === '1' ? <Tag color="blue" style={{ fontSize: '11px' }}>Yes</Tag> : <Tag style={{ fontSize: '11px' }}>No</Tag>;
      },
    },
  ];

  const filteredDisplayData = React.useMemo(() => {
    if (!tableSearchText) {
      return searchResults;
    }
    const lowerSearchText = tableSearchText.toLowerCase();
    return searchResults.filter(item => {
      return Object.values(item).some(value =>
        String(value || '').toLowerCase().includes(lowerSearchText)
      );
    });
  }, [searchResults, tableSearchText]);

  // Separate function to render the image section
  const renderImageSection = (sku: SkuDetailItem | null) => {
    if (!sku) return null;

    return (
      <ProCard 
        title={
          <Space>
            <Text strong>Product Images</Text>
            {sku.image_count !== undefined && (
              <Tag color="blue">{sku.image_count} image{sku.image_count === 1 ? '' : 's'}</Tag>
            )}
          </Space>
        } 
        bordered 
        headerBordered
        style={{ marginTop: 16 }}
        extra={
          hasManageImagePermission ? (
            <Button
              type="primary"
              icon={<PictureOutlined />}
              onClick={() => handleOpenImageGallery(sku.Art_No || '')}
              disabled={!sku.Art_No}
            >
              Manage Images
            </Button>
          ) : (
            <Button
              icon={<PictureOutlined />}
              onClick={() => handleOpenImageGallery(sku.Art_No || '')}
              disabled={!sku.Art_No}
            >
              View Images
            </Button>
          )
        }
      >
        {sku.images && sku.images.length > 0 ? (
          <Row gutter={[16, 16]}>
            {sku.images.slice(0, 4).map((image, index) => (
              <Col xs={12} sm={8} md={6} key={image.id}>
                <div
                  style={{
                    border: '1px solid #d9d9d9',
                    borderRadius: '6px',
                    overflow: 'hidden',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                  }}
                  onClick={() => handleOpenImageGallery(sku.Art_No || '')}
                >
                  <img
                    src={image.image_url}
                    alt={image.filename}
                    style={{
                      width: '100%',
                      height: '120px',
                      objectFit: 'contain',
                      backgroundColor: '#fafafa',
                    }}
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2Y1ZjVmNSIvPgogIDx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiM5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIwLjNlbSI+Tm8gSW1hZ2U8L3RleHQ+Cjwvc3ZnPg==';
                    }}
                  />
                  <div style={{ padding: '8px', background: '#fafafa' }}>
                    <Text style={{ fontSize: '12px' }} ellipsis>
                      {image.filename}
                    </Text>
                  </div>
                </div>
              </Col>
            ))}
            {sku.images.length > 4 && (
              <Col xs={12} sm={8} md={6}>
                <div
                  style={{
                    border: '1px dashed #d9d9d9',
                    borderRadius: '6px',
                    height: '120px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    cursor: 'pointer',
                    background: '#fafafa',
                  }}
                  onClick={() => handleOpenImageGallery(sku.Art_No || '')}
                >
                  <Space direction="vertical" style={{ textAlign: 'center' }}>
                    <PictureOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
                    <Text style={{ fontSize: '12px', color: '#666' }}>
                      +{sku.images.length - 4} more
                    </Text>
                  </Space>
                </div>
              </Col>
            )}
          </Row>
        ) : (
          <Empty
            description="No images available"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            {hasManageImagePermission ? (
              <Button
                type="primary"
                icon={<PictureOutlined />}
                onClick={() => handleOpenImageGallery(sku.Art_No || '')}
                disabled={!sku.Art_No}
              >
                Add Images
              </Button>
            ) : (
              <Text type="secondary">
                Contact your administrator to request image management permission
              </Text>
            )}
          </Empty>
        )}
      </ProCard>
    );
  };

  const renderSkuDetailsSection = (sku: SkuDetailItem | null) => {
    if (!sku) {
      // Don't show this if results are present but no specific item is selected for detail view yet.
      // It will be shown if searchPerformed is true and searchResults is empty.
      if (searchPerformed && searchResults.length > 0) {
         return <ProCard bordered style={{ marginTop: 16 }}><Empty description="Select an item from the results table to see details." /></ProCard>;
      }
      // If no search performed yet, or search yielded no results, this part is handled by other Empty components.
      return null; 
    }
    return (
      <div style={{ marginTop: 16 }}>
        {/* Section 1: Basic Information */}
        <ProCard title={<Text strong>Basic Information</Text>} bordered headerBordered style={{ marginBottom: 16 }}>
          <Descriptions bordered column={{ xs: 1, sm: 1, md: 2 }} layout="horizontal" size="small">
            <Descriptions.Item label="Status" span={1}>
              <Tag color={sku.Active === 'Active' ? 'green' : sku.Active === 'Inactive' ? 'red' : 'orange'}>
                {sku.Active}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="ARMSCode" span={1}>{sku.sku_item_code}</Descriptions.Item>
            <Descriptions.Item label="Art No">{sku.Art_No || 'N/A'}</Descriptions.Item>
            <Descriptions.Item label="MCode">{sku.MCode || 'N/A'}</Descriptions.Item>
            <Descriptions.Item label="Description" span={2}>{sku["ART_NO DESCRIPTION"] || 'N/A'}</Descriptions.Item>
            <Descriptions.Item label="Brand">{sku.Brand || 'N/A'}</Descriptions.Item>
            <Descriptions.Item label="Department">{sku.Department || 'N/A'}</Descriptions.Item>
            <Descriptions.Item label="Color">{sku.Color || 'N/A'}</Descriptions.Item>
            <Descriptions.Item label="Size">{sku.Size || 'N/A'}</Descriptions.Item>
          </Descriptions>
        </ProCard>

        {/* Section 2: Pricing Information */}
        <ProCard title={<Text strong>Pricing Information</Text>} bordered headerBordered style={{ marginBottom: 16 }}>
          <Descriptions bordered column={{ xs: 1, sm: 1, md: 2 }} layout="horizontal" size="small">
            <Descriptions.Item label="RSP">{sku.RSP ? `RM ${sku.RSP.toFixed(2)}` : 'N/A'}</Descriptions.Item>
            <Descriptions.Item label="RSP Discount">
              <Tag color="orange" style={{ fontWeight: 'bold' }}>
                {sku.RSP_Discount || 'N/A'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="Net Selling Price" span={2}>
              <Tag color="green" style={{ fontWeight: 'bold', fontSize: '14px' }}>
                {sku.Net_Selling_Price ? `RM ${sku.Net_Selling_Price.toFixed(2)}` : 'N/A'}
              </Tag>
            </Descriptions.Item>
          </Descriptions>
        </ProCard>

        {/* Section 3: Extra Information */}
        <ProCard title={<Text strong>Extra Information</Text>} bordered headerBordered style={{ marginBottom: 16 }}>
          <Descriptions bordered column={{ xs: 1, sm: 1, md: 2 }} layout="horizontal" size="small">
            <Descriptions.Item label="Cost Price">
              {hasCostPermission 
                ? (sku.Cost_Price ? `RM ${sku.Cost_Price.toFixed(2)}` : 'N/A')
                : '[No View Permission]'
              }
            </Descriptions.Item>
            <Descriptions.Item label="HQ Cost">
              {hasCostPermission 
                ? (sku.HQ_Cost ? `RM ${sku.HQ_Cost.toFixed(2)}` : 'N/A')
                : '[No View Permission]'
              }
            </Descriptions.Item>
            <Descriptions.Item label="Vendor">{sku.Vendor || 'N/A'}</Descriptions.Item>
            <Descriptions.Item label="Season Code">{sku.Season_Code || 'N/A'}</Descriptions.Item>
            <Descriptions.Item label="Category">{sku.Category || 'N/A'}</Descriptions.Item>
            <Descriptions.Item label="Silhouette">{sku.Silhouette || 'N/A'}</Descriptions.Item>
            <Descriptions.Item label="Type of Silhouette" span={2}>{sku.Type_of_Silhouette || 'N/A'}</Descriptions.Item>
            <Descriptions.Item label="Gender">{sku.Gender || 'N/A'}</Descriptions.Item>
            <Descriptions.Item label="Age">{sku.Age || 'N/A'}</Descriptions.Item>
            <Descriptions.Item label="Is Parent">{sku.is_parent === '1' ? 'Yes' : sku.is_parent === '0' ? 'No' : 'N/A'}</Descriptions.Item>
            <Descriptions.Item label="Last Update">{sku.lastupdate ? new Date(sku.lastupdate).toLocaleString() : 'N/A'}</Descriptions.Item>
          </Descriptions>
        </ProCard>


      </div>
    );
  };

  const handleExport = async () => {
    // Basic validation: At least one search field must be provided
    if (!searchValue.trim()) {
      message.warning('Please provide search criteria before exporting.');
      return;
    }

    // Additional validation: Check if search has been performed and results exist
    if (!searchPerformed || searchResults.length === 0) {
      message.warning('Please perform a search first to ensure there are results to export.');
      return;
    }

    const exportParams: SkuCheckerExportRequest = {
      sku_item_code: searchType === 'sku_item_code' ? searchValue : undefined,
      artno: searchType === 'artno' ? searchValue : undefined,
      mcode: searchType === 'mcode' ? searchValue : undefined,
      description: searchType === 'description' ? searchValue : undefined,
      search_scope: searchScope,
      use_wildcard: useWildcard,
    };

    message.loading({ content: 'Starting export request...', key: 'export' });

    try {
      const response = await exportSkuChecker(exportParams);
      if (response.success) {
        message.success({ 
          content: response.message || 'Export started successfully! Check history below.', 
          key: 'export', 
          duration: 5 
        });
      } else {
        message.error({ 
          content: response.message || 'Failed to start export.', 
          key: 'export', 
          duration: 5 
        });
      }
    } catch (e: any) {
      console.error("Export error:", e);
      message.error({ 
        content: e.message || 'An unexpected error occurred during export.', 
        key: 'export', 
        duration: 5 
      });
    }
  };

  // Handle export from large result modal
  const handleExportFromModal = async () => {
    if (!currentSearchParams) {
      message.error('Search parameters not available for export.');
      return;
    }

    const exportParams: SkuCheckerExportRequest = {
      sku_item_code: currentSearchParams.sku_item_code,
      artno: currentSearchParams.artno,
      mcode: currentSearchParams.mcode,
      description: currentSearchParams.description,
      search_scope: currentSearchParams.search_scope,
      use_wildcard: currentSearchParams.use_wildcard,
    };

    message.loading({ content: 'Starting export request...', key: 'export' });

    try {
      const response = await exportSkuChecker(exportParams);
      if (response.success) {
        message.success({ 
          content: response.message || 'Export started successfully! Check history below.', 
          key: 'export', 
          duration: 5 
        });
        setIsLargeResultModalVisible(false); // Close modal on successful export
      } else {
        message.error({ 
          content: response.message || 'Failed to start export.', 
          key: 'export', 
          duration: 5 
        });
      }
    } catch (e: any) {
      console.error("Export error:", e);
      message.error({ 
        content: e.message || 'An unexpected error occurred during export.', 
        key: 'export', 
        duration: 5 
      });
    }
  };

  const { initialState } = useModel('@@initialState');
  const hasCostPermission = useHasCostPermission();
  const hasManageImagePermission = useHasManageSkuImagePermission();

  // Scanner Functions
  const initializeScanner = async () => {
    if (!isScannerModalVisible || !interactiveRef.current || quaggaInitializedRef.current) {
        return;
    }

    setScannerInitializing(true);
    setScannerError(null);

    try {
        if (typeof Quagga === 'undefined') {
            setScannerError("Barcode scanner library (QuaggaJS) is not available. Please ensure it's installed and imported correctly.");
            setScannerInitializing(false);
            return;
        }

        Quagga.init({
            inputStream: {
                name: "Live",
                type: "LiveStream",
                target: interactiveRef.current,
                constraints: { width: { min: 640 }, height: { min: 480 }, facingMode: { ideal: "environment" } }, 
            },
            locator: { patchSize: "medium", halfSample: false },
            numOfWorkers: navigator.hardwareConcurrency || 2,
            decoder: { readers: ["code_128_reader", "ean_reader", "ean_8_reader", "code_39_reader", "upc_reader", "upc_e_reader"] },
            locate: true,
            frequency: 5,
        }, (err: any) => {
            setScannerInitializing(false);
            if (err) {
                console.error("Quagga Init Error:", err);
                let errorMsg = "Camera initialization failed.";
                if (err.name === 'NotAllowedError') errorMsg = "Camera access denied. Please enable camera permissions.";
                else if (err.name === 'NotFoundError' || err.name === 'DevicesNotFoundError') errorMsg = "No suitable camera found.";
                else if (err.name === 'NotReadableError' || err.name === 'TrackStartError') errorMsg = "Camera is already in use or cannot be accessed.";
                setScannerError(errorMsg);
                quaggaInitializedRef.current = false;
                return;
            }
            console.log("Quagga initialization finished. Starting scanner...");
            Quagga.start();
            quaggaInitializedRef.current = true;
        });

        Quagga.onDetected((result: any) => {
            const code = result.codeResult.code;
            if (code) {
                console.log("Barcode Detected:", code);
                // For SKU checker, we'll set the scanned code as the search value
                // and set search type to MCode as our generated barcodes encode MCode
                setSearchValue(code);
                setSearchType('mcode'); // Set to MCode as our barcodes encode MCode values
                setIsScannerModalVisible(false);
                setTriggerSearch(true); // Set trigger for auto search
            }
        });

        Quagga.onProcessed((result: any) => {
            if (!quaggaInitializedRef.current || !interactiveRef.current) return;
            try {
                const drawingCtx = Quagga.canvas.ctx.overlay as CanvasRenderingContext2D;
                const drawingCanvas = Quagga.canvas.dom.overlay;

                if (result && drawingCanvas instanceof HTMLCanvasElement && drawingCtx) {
                    drawingCtx.clearRect(0, 0, drawingCanvas.width, drawingCanvas.height);

                    if (result.boxes) {
                        result.boxes
                            .filter((box: any) => box !== result.box)
                            .forEach((box: any) => {
                                Quagga.ImageDebug.drawPath(box, { x: 0, y: 1 }, drawingCtx, {
                                    color: "green",
                                    lineWidth: 2,
                                });
                            });
                    }

                    if (result.box) {
                        Quagga.ImageDebug.drawPath(result.box, { x: 0, y: 1 }, drawingCtx, {
                            color: "blue",
                            lineWidth: 2,
                        });
                    }
                }
            } catch (e) {
                // console.warn("Error during Quagga.onProcessed drawing:", e);
            }
        });

    } catch (error: any) {
        console.error("Error initializing scanner:", error);
        setScannerError(error.message || "Failed to initialize barcode scanner.");
        setScannerInitializing(false);
    }
  };

  const stopScanner = () => {
    if (quaggaInitializedRef.current && typeof Quagga !== 'undefined') {
        try {
            Quagga.stop();
            console.log("Quagga scanner stopped.");
        } catch (e) {
            console.error("Error stopping Quagga:", e);
        }
        // Clear canvas if Quagga doesn't do it on stop
        if (interactiveRef.current) {
            const canvasElement = interactiveRef.current.querySelector('canvas.drawingBuffer');
            if (canvasElement instanceof HTMLCanvasElement) {
                const context = canvasElement.getContext('2d');
                if (context) {
                    context.clearRect(0, 0, canvasElement.width, canvasElement.height);
                }
            }
            // Remove video element as well to ensure camera light turns off
            const video = interactiveRef.current.querySelector('video');
            if(video) video.remove();
        }
        quaggaInitializedRef.current = false;
    }
  };

  const handleOpenScanner = () => {
    setScannerError(null);
    setIsScannerModalVisible(true);
  };

  const handleModalClose = () => {
    setIsScannerModalVisible(false);
  };

  const handleOpenImageGallery = (artno: string) => {
    setCurrentArtno(artno);
    setImageGalleryVisible(true);
  };

  const handleCloseImageGallery = () => {
    setImageGalleryVisible(false);
    setCurrentArtno('');
  };

  const handleImagesUpdated = async () => {
    // If a SKU is currently selected, refresh its image count
    if (selectedSku) {
      try {
        const params: FetchSkuDetailsParams = {
          page_size: 1,
          page: 1,
          search_scope: searchScope,
        };

        if (selectedSku.Art_No) {
          params.artno = selectedSku.Art_No;
        } else if (selectedSku.sku_item_code) {
          params.sku_item_code = selectedSku.sku_item_code;
        }

        const response = await fetchSkuDetails(params);
        if (response.success && response.data && response.data.length > 0) {
          const updatedSku = response.data[0];
          setSelectedSku(updatedSku);
          
          // Also update the item in searchResults if it exists
          setSearchResults(prevResults => 
            prevResults.map(item => 
              item.sku_item_code === updatedSku.sku_item_code ? updatedSku : item
            )
          );
        }
      } catch (error) {
        console.error('Error refreshing SKU data:', error);
      }
    }
  };

  // Barcode generator functions
  const handleOpenBarcodeGenerator = () => {
    if (selectedRows.length === 0) {
      message.warning('Please select at least one SKU to generate barcodes');
      return;
    }
    setBarcodeModalVisible(true);
  };

  const handleCloseBarcodeModal = () => {
    setBarcodeModalVisible(false);
  };

  const handleOpenQOHModal = (sku: SkuDetailItem) => {
    setQohSelectedSku(sku);
    setQohModalVisible(true);
  };

  const handleCloseQOHModal = () => {
    setQohModalVisible(false);
    setQohSelectedSku(null);
  };

  const handleOpenSalesModal = async (sku: SkuDetailItem) => {
    // Try multiple possible field names to find Art No and MCode
    const artno = sku.Art_No || (sku as any).artno || (sku as any).art_no;
    const mcode = sku.MCode || (sku as any).mcode || (sku as any).Mcode;
    
    // Debug logging to see what fields are available
    console.log('Sales modal SKU data:', {
      Art_No: sku.Art_No,
      MCode: sku.MCode,
      extracted_artno: artno,
      extracted_mcode: mcode,
      searchType: mcode ? 'child_code' : 'parent_code',
      fullSku: sku
    });
    
    if (!artno) {
      message.error(`Cannot fetch sales data: Art No is missing. Available fields: ${Object.keys(sku).join(', ')}`);
      return;
    }
    
    // For parent codes, mcode will be missing - this is normal and expected
    if (!mcode) {
      console.log('No MCode found - treating as parent code search for all child variants');
    }

    setSalesSelectedSku(sku);
    setSalesModalVisible(true);
    setSalesLoading(true);
    setSalesData(null);

    try {
      // Pass mcode only if it exists, otherwise search for parent code (all children)
      const response = await fetchSkuSalesSummary(artno, mcode || undefined);
      setSalesData(response);
      
      if (!response.success) {
        message.warning(response.message || 'Failed to fetch sales data');
      }
    } catch (error) {
      console.error('Error fetching sales summary:', error);
      message.error('Failed to fetch sales summary');
    } finally {
      setSalesLoading(false);
    }
  };

  const handleCloseSalesModal = () => {
    setSalesModalVisible(false);
    setSalesSelectedSku(null);
    setSalesData(null);
    setSalesLoading(false);
    setSalesSearchText('');
  };

  const generateBarcodePDF = async () => {
    if (selectedRows.length === 0) {
      message.error('No barcodes to generate');
      return;
    }

    setGeneratingPDF(true);
    try {
      // Create PDF with A4 size in portrait mode
      const pdf = new jsPDF('p', 'mm', 'a4');
      const pageWidth = 210; // A4 width in mm
      const pageHeight = 297; // A4 height in mm
      
      // Barcode dimensions (60mm x 50mm as requested)
      const barcodeWidth = 60;
      const barcodeHeight = 50;
      
      // Calculate layout
      const margin = 10; // 10mm margin
      const gap = 5; // 5mm gap between barcodes
      const availableWidth = pageWidth - (2 * margin);
      const availableHeight = pageHeight - (2 * margin);
      
      // 2 columns as requested
      const cols = 2;
      const colWidth = (availableWidth - gap) / cols;
      
      // Calculate how many rows fit per page
      const rowsPerPage = Math.floor((availableHeight + gap) / (barcodeHeight + gap));
      const barcodesPerPage = cols * rowsPerPage;
      
      let currentPage = 0;
      
      for (let i = 0; i < selectedRows.length; i++) {
        const item = selectedRows[i];
        
        // Add new page if needed
        if (i % barcodesPerPage === 0) {
          if (currentPage > 0) {
            pdf.addPage();
          }
          currentPage++;
        }
        
        // Calculate position
        const indexOnPage = i % barcodesPerPage;
        const row = Math.floor(indexOnPage / cols);
        const col = indexOnPage % cols;
        
        const x = margin + col * (colWidth + gap);
        const y = margin + row * (barcodeHeight + gap);
        
        // Draw border
        pdf.setDrawColor(200, 200, 200);
        pdf.setLineWidth(0.1);
        pdf.rect(x, y, barcodeWidth, barcodeHeight);
        
        if (item.MCode) {
          // Generate barcode as image using MCode
          const canvas = document.createElement('canvas');
          const canvasWidth = 400; // Higher resolution
          const canvasHeight = 60;
          canvas.width = canvasWidth;
          canvas.height = canvasHeight;
          
          try {
            JsBarcode(canvas, item.MCode, {
              format: 'CODE128',
              width: 2,
              height: 50,
              fontSize: 12,
              margin: 5,
              background: '#ffffff',
              lineColor: '#000000',
              text: item.MCode, // Show MCode in barcode tooltip
            });
            
            // Add barcode to PDF
            const barcodeImgData = canvas.toDataURL('image/png');
            pdf.addImage(barcodeImgData, 'PNG', x + 2, y + 2, barcodeWidth - 4, 15);
          } catch (error) {
            console.error('Error generating barcode for', item.MCode, error);
          }
        }
        
        // Add text content
        pdf.setFontSize(8);
        pdf.setFont('helvetica', 'bold');
        
        // Product description with word wrap (smaller font, shorter lines)
        const description = item["ART_NO DESCRIPTION"] || 'N/A';
        const maxLineLength = 22; // Shorter line length
        const words = description.split(' ');
        let line1 = '';
        let line2 = '';
        
        // Build first line
        for (const word of words) {
          if ((line1 + word).length <= maxLineLength) {
            line1 += (line1 ? ' ' : '') + word;
          } else {
            // Start second line
            if ((line2 + word).length <= maxLineLength) {
              line2 += (line2 ? ' ' : '') + word;
            } else {
              // Truncate if still too long
              if (line2.length === 0) {
                // If second line is empty, try to fit part of the word
                line2 = word.substring(0, maxLineLength - 3) + '...';
              } else {
                line2 = line2 + '...';
              }
              break;
            }
          }
        }
        
        // Truncate first line if too long
        if (line1.length > maxLineLength) {
          line1 = line1.substring(0, maxLineLength - 3) + '...';
          line2 = '';
        }
        
        pdf.text(line1, x + 2, y + 22);
        if (line2) {
          pdf.text(line2, x + 2, y + 26);
        }
        
        // Size and Color on same line with spacing (adjust position based on description lines)
        const sizeColorY = line2 ? y + 31 : y + 27;
        pdf.setFontSize(9);
        pdf.setFont('helvetica', 'normal');
        const sizeText = `Size: ${item.Size || 'N/A'}`;
        const colorText = `Color: ${item.Color || 'N/A'}`;
        pdf.text(sizeText, x + 2, sizeColorY);
        pdf.text(colorText, x + 32, sizeColorY);
        
        // Net Price and RSP with spacing
        const priceY = sizeColorY + 5;
        pdf.setFontSize(9);
        pdf.setFont('helvetica', 'bold');
        const netPrice = `Net: RM ${item.Net_Selling_Price ? item.Net_Selling_Price.toFixed(2) : 'N/A'}`;
        pdf.text(netPrice, x + 2, priceY);
        
        // RSP with strikethrough
        if (item.RSP) {
          const rspText = `RSP: RM ${item.RSP.toFixed(2)}`;
          pdf.setFont('helvetica', 'normal');
          pdf.setFontSize(9);
          pdf.text(rspText, x + 32, priceY);
          
          // Draw line through RSP text
          const rspWidth = pdf.getTextWidth(rspText);
          pdf.setDrawColor(0, 0, 0);
          pdf.setLineWidth(0.2);
          pdf.line(x + 32, priceY - 1, x + 32 + rspWidth, priceY - 1);
        }
        
        // Add Art No at bottom
        pdf.setFontSize(7);
        pdf.setFont('helvetica', 'normal');
        pdf.text(item.Art_No || 'N/A', x + 2, y + 47);
      }
      
      // Generate filename with timestamp
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
      const filename = `barcodes_${selectedRows.length}_items_${timestamp}.pdf`;
      
      pdf.save(filename);
      message.success(`PDF generated successfully: ${filename}`);
      
    } catch (error) {
      console.error('Error generating PDF:', error);
      message.error('Failed to generate PDF');
    } finally {
      setGeneratingPDF(false);
    }
  };

  const renderBarcodePreview = () => {
    if (selectedRows.length === 0) return null;

    return (
      <div 
        ref={barcodePreviewRef}
        style={{ 
          display: 'grid',
          gridTemplateColumns: 'repeat(2, 1fr)',
          gap: '10px',
          padding: '20px',
          backgroundColor: '#ffffff',
          maxHeight: '500px',
          overflowY: 'auto'
        }}
      >
        {selectedRows.map((item, index) => (
          <div
            key={`${item.sku_item_code}-${index}`}
            style={{
              width: '60mm',
              height: '50mm',
              border: '1px solid #ddd',
              padding: '4px',
              backgroundColor: '#ffffff',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'space-between',
              fontSize: '10px',
              fontFamily: 'Arial, sans-serif'
            }}
          >
            {/* Barcode */}
            <div style={{ textAlign: 'center', marginBottom: '4px' }}>
              {item.MCode ? (
                <canvas
                  ref={(canvas) => {
                    if (canvas && item.MCode) {
                      try {
                        JsBarcode(canvas, item.MCode, {
                          format: 'CODE128',
                          width: 1.5,
                          height: 30,
                          fontSize: 8,
                          margin: 2,
                          background: '#ffffff',
                          lineColor: '#000000',
                          text: item.MCode, // Show MCode in barcode tooltip
                        });
                      } catch (error) {
                        console.error('Error generating barcode preview for', item.MCode, error);
                      }
                    }
                  }}
                  style={{ maxWidth: '100%', height: 'auto' }}
                />
              ) : (
                <div style={{ height: '30px', backgroundColor: '#f5f5f5', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '8px' }}>
                  No MCode
                </div>
              )}
            </div>

            {/* Product Info */}
            <div style={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
              {/* Description with word wrap */}
              <div style={{ fontWeight: 'bold', fontSize: '8px', marginBottom: '4px', lineHeight: '1.2', wordWrap: 'break-word' }}>
                {(() => {
                  const description = item["ART_NO DESCRIPTION"] || 'N/A';
                  const maxLineLength = 22; // Shorter line length
                  const words = description.split(' ');
                  let line1 = '';
                  let line2 = '';
                  
                  // Build first line
                  for (const word of words) {
                    if ((line1 + word).length <= maxLineLength) {
                      line1 += (line1 ? ' ' : '') + word;
                    } else {
                      // Start second line
                      if ((line2 + word).length <= maxLineLength) {
                        line2 += (line2 ? ' ' : '') + word;
                      } else {
                        // Truncate if still too long
                        if (line2.length === 0) {
                          // If second line is empty, try to fit part of the word
                          line2 = word.substring(0, maxLineLength - 3) + '...';
                        } else {
                          line2 = line2 + '...';
                        }
                        break;
                      }
                    }
                  }
                  
                  // Truncate first line if too long
                  if (line1.length > maxLineLength) {
                    line1 = line1.substring(0, maxLineLength - 3) + '...';
                    line2 = '';
                  }
                  
                  return (
                    <>
                      <div>{line1}</div>
                      {line2 && <div>{line2}</div>}
                    </>
                  );
                })()}
              </div>

              {/* Size and Color */}
              <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '9px', marginBottom: '4px' }}>
                <span>Size: {item.Size || 'N/A'}</span>
                <span>Color: {item.Color || 'N/A'}</span>
              </div>

              {/* Prices */}
              <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '9px', marginBottom: '4px' }}>
                <span style={{ fontWeight: 'bold' }}>
                  Net: RM {item.Net_Selling_Price ? item.Net_Selling_Price.toFixed(2) : 'N/A'}
                </span>
                {item.RSP && (
                  <span style={{ textDecoration: 'line-through', color: '#999' }}>
                    RSP: RM {item.RSP.toFixed(2)}
                  </span>
                )}
              </div>

              {/* Art No at bottom */}
              <div style={{ fontSize: '7px', color: '#666', textAlign: 'left' }}>
                {item.Art_No || 'N/A'}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Effect to manage scanner lifecycle based on modal visibility
  useEffect(() => {
    if (isScannerModalVisible) {
        const timer = setTimeout(() => {
            initializeScanner();
        }, 100);
        return () => clearTimeout(timer);
    } else {
        stopScanner();
    }
  }, [isScannerModalVisible]);

  // Effect to trigger search after scan
  useEffect(() => {
    if (triggerSearch) {
      if (searchValue.trim()) {
        handleSearch();
      }
      setTriggerSearch(false);
    }
  }, [triggerSearch, searchValue]);

  return (
    <ConfigProvider locale={enUS}>
      <PageContainer title="SKU Checker">
        <ProCard direction="column" ghost gutter={[0, 16]}>
          <ProCard>
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              <Row gutter={[16, 24]} style={{width: '100%'}}>
                <Col xs={24} md={12}>
                  <Space direction="vertical" style={{ width: '100%' }} size="middle">
                    <Text strong>Search By:</Text>
                    <Radio.Group
                      buttonStyle="solid"
                      value={searchType}
                      onChange={(e) => {
                        const newSearchType = e.target.value as SearchType;
                        setSearchType(newSearchType);
                        // Logic to update searchScope and its disabled status is now in useEffect
                      }}
                      style={{ width: '100%' }}
                    >
                      <Radio.Button value="artno">Art No</Radio.Button>
                      <Radio.Button value="mcode">MCode</Radio.Button>
                      <Radio.Button value="sku_item_code">ARMSCode</Radio.Button>
                      <Radio.Button value="description">Description</Radio.Button>
                    </Radio.Group>
                  </Space>
                </Col>
                <Col xs={24} md={12}>
                  <Space direction="vertical" style={{ width: '100%' }} size="middle">
                    <Text strong>Search Scope:</Text>
                    <Radio.Group
                      buttonStyle="solid"
                      value={searchScope}
                      onChange={(e) => setSearchScope(e.target.value as SearchScope)}
                      disabled={searchScopeDisabled}
                      style={{ width: '100%' }}
                    >
                      <Radio.Button value="parent">Parent Code Only</Radio.Button>
                      <Radio.Button value="all">All Codes (Parent & Child)</Radio.Button>
                    </Radio.Group>
                  </Space>
                </Col>
              </Row>
              
              {isCodeSearchType() ? (
                <Input.TextArea
                  rows={3}
                  placeholder={getSearchPlaceholder()}
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.target.value)}
                  style={{ width: '100%' }}
                />
              ) : (
                <Input
                  placeholder={getSearchPlaceholder()}
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.target.value)}
                  onPressEnter={() => handleSearch()}
                  style={{ width: '100%' }}
                />
              )}

              {isCodeSearchType() && (
                <Checkbox
                  checked={useWildcard}
                  onChange={(e) => setUseWildcard(e.target.checked)}
                >
                  Use Wildcard Search (e.g., %value%) for {searchTypeLabels[searchType]}
                </Checkbox>
              )}

              {/* Standardized Button Row */}
              <Row gutter={[16, 16]} style={{ width: '100%' }}>
                <Col xs={24} sm={8} md={6}>
                  <Button 
                    type="primary" 
                    icon={<SearchOutlined />}
                    onClick={() => handleSearch()} 
                    loading={loading}
                    style={{ width: '100%' }}
                  >
                    Search
                  </Button>
                </Col>
                <Col xs={24} sm={8} md={6}>
                  <Button 
                    onClick={handleReset}
                    style={{ 
                      width: '100%',
                      backgroundColor: '#ff4d4f', 
                      color: 'white', 
                      borderColor: '#ff4d4f'
                    }}
                  >
                    Reset
                  </Button>
                </Col>
                <Col xs={24} sm={8} md={6}>
                  <Button 
                    icon={<DownloadOutlined />}
                    onClick={handleExport} 
                    style={{ 
                      backgroundColor: '#217346', 
                      color: 'white', 
                      borderColor: '#217346',
                      width: '100%'
                    }}
                    disabled={!searchValue.trim()}
                  >
                    Export to Excel
                  </Button>
                </Col>
                <Col xs={24} sm={8} md={6}>
                  <Button 
                    icon={<CameraOutlined />}
                    onClick={handleOpenScanner}
                    disabled={!!(scannerError && scannerError.startsWith("Could not load"))}
                    style={{ width: '100%' }}
                  >
                    Scan Barcode
                  </Button>
                </Col>
                <Col xs={24} sm={8} md={6}>
                  <Button 
                    icon={<QrcodeOutlined />}
                    onClick={handleOpenBarcodeGenerator}
                    disabled={selectedRows.length === 0}
                    style={{ 
                      width: '100%',
                      backgroundColor: '#722ed1',
                      color: 'white',
                      borderColor: '#722ed1'
                    }}
                  >
                    Generate Barcode ({selectedRows.length})
                  </Button>
                </Col>
              </Row>
            </Space>
          </ProCard>

          {loading && <div style={{textAlign: 'center', padding: '20px'}}><Spin tip="Loading SKU details..." size="large"/></div>}
          
          {error && !loading && (
            <Alert message={error} type="error" showIcon style={{ marginBottom: 16 }} />
          )}

          {!loading && searchPerformed && searchResults.length === 0 && !error && (
            <Empty description="No SKU found matching your criteria. Try different search terms or options." />
          )}
          
          {searchResults.length > 0 && !loading && (
            <ProTable<SkuDetailItem>
              headerTitle="Search Results"
              columns={columns}
              dataSource={filteredDisplayData}
              rowKey="sku_item_code"
              search={false}
              rowSelection={{
                type: 'checkbox',
                selectedRowKeys,
                onChange: (newSelectedRowKeys: React.Key[], newSelectedRows: SkuDetailItem[]) => {
                  setSelectedRowKeys(newSelectedRowKeys);
                  setSelectedRows(newSelectedRows);
                },
                getCheckboxProps: (record: SkuDetailItem) => ({
                  disabled: !record.Art_No, // Disable selection if no Art_No for barcode generation
                  name: record.sku_item_code,
                }),
              }}
              toolBarRender={() => [
                <Input.Search
                  key="table-search"
                  placeholder="Search in results..."
                  allowClear
                  value={tableSearchText}
                  onChange={(e) => setTableSearchText(e.target.value)}
                  onSearch={(value) => setTableSearchText(value)}
                  style={{ width: 250 }}
                />,
              ]}
              pagination={{ 
                current: currentPage,
                pageSize: currentPageSize,
                total: filteredDisplayData.length, // Use client-side filtered data length
                showSizeChanger: true,
                pageSizeOptions: ['10', '20', '50', '100', '200', '500'],
                showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
                onChange: (page, pageSize) => {
                  setCurrentPage(page);
                  setCurrentPageSize(pageSize);
                  // No API call needed - client-side pagination
                },
                size: 'small'
              }}
              size="small"
              scroll={{ x: 'max-content', y: 400 }}
              tableStyle={{ 
                fontSize: '12px'
              }}
              onRow={(record) => {
                return {
                  onClick: () => {
                    setSelectedSku(record);
                  },
                };
              }}
              rowClassName={(record) => (record.sku_item_code === selectedSku?.sku_item_code ? 'ant-table-row-selected' : '')}
            />
          )}
          
          {/* Product Images Section - displayed after table, before basic information */}
          {renderImageSection(selectedSku)}
          
          {/* Basic Information Sections */}
          {renderSkuDetailsSection(selectedSku)}

        </ProCard>

        {/* Export History Component */}
        <ExportHistory
          reportNameFilter="SKU Checker"
        />

        {/* Scanner Modal */}
        <Modal
          title="Scan Barcode"
          open={isScannerModalVisible}
          onCancel={handleModalClose}
          footer={null}
          destroyOnClose
          width="80%"
          style={{ maxWidth: '600px' }}
        >
          {scannerInitializing && <Spin tip="Initializing camera..."><div style={{height: '50px', display:'flex', alignItems:'center', justifyContent:'center'}}>Initializing...</div></Spin>}
          {scannerError && !scannerInitializing && <Alert message="Scanner Error" description={scannerError} type="error" showIcon style={{marginBottom: '10px'}}/>}
          <div ref={interactiveRef} style={{
            position: 'relative',
            width: '100%',
            paddingTop: '75%', // 4:3 aspect ratio
            backgroundColor: '#000',
            overflow: 'hidden',
          }}>
            {isScannerModalVisible && !scannerInitializing && !scannerError && (
                <div style={{
                  position: 'absolute',
                  top: 0, right: 0, bottom: 0, left: 0,
                  zIndex: 2,
                }}>
                    <div style={{
                      position: 'absolute',
                      left: '10%', right: '10%',
                      top: '20%', bottom: '20%',
                      border: '2px solid rgba(255, 255, 255, 0.7)',
                      borderRadius: '8px',
                      boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.6)',
                    }}></div>
                    <div style={{
                      position: 'absolute',
                      left: '5%', right: '5%', 
                      top: '50%',
                      height: '2px',
                      background: 'red',
                      boxShadow: '0 0 5px red',
                      zIndex: 10,
                      animation: 'scanLaserAnimation 2s infinite linear'
                    }}></div>
                </div>
            )}
          </div>
        </Modal>

        {/* Add CSS for scanner animations, video styling, and responsive table */}
        <style>
          {`@keyframes scanLaserAnimation {
              0% { transform: translateY(-50px); }
              50% { transform: translateY(50px); }
              100% { transform: translateY(-50px); }
          }
          .drawingBuffer, video {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              object-fit: cover;
          }
          
          /* Responsive table improvements */
          @media (max-width: 768px) {
            .ant-pro-table .ant-table-thead > tr > th {
              font-size: 11px !important;
              padding: 8px 4px !important;
              white-space: nowrap;
            }
            .ant-pro-table .ant-table-tbody > tr > td {
              font-size: 11px !important;
              padding: 6px 4px !important;
            }
            .ant-tag {
              font-size: 10px !important;
              padding: 0 4px !important;
              line-height: 16px !important;
            }
          }
          
          @media (max-width: 576px) {
            .ant-pro-table .ant-table-thead > tr > th {
              font-size: 10px !important;
              padding: 6px 2px !important;
            }
            .ant-pro-table .ant-table-tbody > tr > td {
              font-size: 10px !important;
              padding: 4px 2px !important;
            }
            .ant-tag {
              font-size: 9px !important;
              padding: 0 2px !important;
              line-height: 14px !important;
            }
          }
          `}
        </style>

        {/* Large Result Set Modal */}
        <Modal
          title="Large Result Set Detected"
          open={isLargeResultModalVisible}
          onCancel={() => setIsLargeResultModalVisible(false)}
          footer={[
            <Button key="cancel" onClick={() => setIsLargeResultModalVisible(false)}>
              Continue Viewing
            </Button>,
            <Button key="export" type="primary" icon={<DownloadOutlined />} onClick={handleExportFromModal}>
              Export to Excel
            </Button>,
          ]}
          width={600}
        >
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            <Alert
              message="Performance Notice"
              description={largeResultMessage}
              type="warning"
              showIcon
            />
            <div>
              <Text>
                For the best performance and to access all results, we recommend using the Export to Excel feature. 
                This will generate a complete file with all {apiTotalResults.toLocaleString()} results that you can download once ready.
              </Text>
            </div>
            <div>
              <Text strong>Options:</Text>
              <ul style={{ marginTop: 8, marginBottom: 0 }}>
                <li><Text strong>Continue Viewing:</Text> Browse the limited results displayed in the table above</li>
                <li><Text strong>Export to Excel:</Text> Generate a complete file with all results for download</li>
              </ul>
            </div>
          </Space>
        </Modal>

        {/* SKU Image Gallery */}
        <SkuImageGallery
          artno={currentArtno}
          visible={imageGalleryVisible}
          onClose={handleCloseImageGallery}
          onImagesUpdated={handleImagesUpdated}
        />

        {/* Barcode Generator Modal */}
        <Modal
          title={`Generate Barcodes (${selectedRows.length} items selected)`}
          open={barcodeModalVisible}
          onCancel={handleCloseBarcodeModal}
          footer={[
            <Button key="cancel" onClick={handleCloseBarcodeModal}>
              Cancel
            </Button>,
            <Button 
              key="generate" 
              type="primary" 
              icon={<DownloadOutlined />}
              onClick={generateBarcodePDF} 
              loading={generatingPDF}
              disabled={selectedRows.length === 0}
            >
              Generate PDF
            </Button>,
          ]}
          destroyOnClose
          width="90%"
          style={{ maxWidth: '800px', top: 20 }}
        >
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            <Alert
              message="Barcode Preview"
              description={`${selectedRows.length} barcode(s) will be generated in Code128 format. Layout: 60mm x 50mm per barcode, 2 columns per page.`}
              type="info"
              showIcon
            />
            
            {generatingPDF && (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <Spin size="large" />
                <div style={{ marginTop: 10 }}>Generating PDF...</div>
              </div>
            )}
            
            {!generatingPDF && (
              <div style={{ maxHeight: '60vh', overflowY: 'auto', border: '1px solid #f0f0f0', borderRadius: '6px' }}>
                {renderBarcodePreview()}
              </div>
            )}
          </Space>
        </Modal>

        {/* Sales Summary Modal */}
        <Modal
          title={
            salesSelectedSku?.MCode 
              ? `Sales Summary - ${salesSelectedSku?.Art_No || 'N/A'} (${salesSelectedSku?.MCode})`
              : `Sales Summary - ${salesSelectedSku?.Art_No || 'N/A'} (All Child Codes)`
          }
          open={salesModalVisible}
          onCancel={handleCloseSalesModal}
          footer={[
            <Button key="close" onClick={handleCloseSalesModal}>
              Close
            </Button>,
          ]}
          width={800}
          destroyOnClose
        >
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            {salesLoading && (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <Spin size="large" />
                <div style={{ marginTop: 10 }}>Loading sales data...</div>
              </div>
            )}
            
            {!salesLoading && salesData && (
              <>
                {salesData.success ? (
                  <>
                    <Descriptions title="Sales Overview" bordered size="small" column={1}>
                      <Descriptions.Item label="Article Number">{salesData.artno}</Descriptions.Item>
                      <Descriptions.Item label="Search Type">
                        {!salesData.mcode || salesData.mcode === 'ALL_CHILDREN' ? (
                          <Tag color="blue">Parent Code (All Child Variants)</Tag>
                        ) : (
                          <Tag color="green">Specific SKU ({salesData.mcode})</Tag>
                        )}
                      </Descriptions.Item>
                      <Descriptions.Item label="Total Quantity Sold">
                        <Text strong style={{ color: '#1890ff', fontSize: '16px' }}>
                          {salesData.total_qty_sold.toLocaleString()}
                        </Text>
                      </Descriptions.Item>

                      <Descriptions.Item label="Branches with Sales">
                        <Text strong>{salesData.branch_count}</Text>
                      </Descriptions.Item>
                      <Descriptions.Item label="Sales Period">
                        {salesData.first_sale_date && salesData.last_sale_date ? (
                          <Text>{salesData.first_sale_date} to {salesData.last_sale_date}</Text>
                        ) : (
                          <Text type="secondary">N/A</Text>
                        )}
                      </Descriptions.Item>
                    </Descriptions>

                    {salesData.branch_details && salesData.branch_details.length > 0 && (
                      <div>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
                          <Text strong style={{ fontSize: '16px' }}>
                            Branch Breakdown
                          </Text>
                          <Input
                            placeholder="Search branches..."
                            value={salesSearchText}
                            onChange={(e) => setSalesSearchText(e.target.value)}
                            allowClear
                            style={{ width: 250 }}
                            prefix={<SearchOutlined />}
                          />
                        </div>
                        <ProTable<any>
                          columns={[
                            {
                              title: 'Branch Code',
                              dataIndex: 'branch_code',
                              key: 'branch_code',
                              width: 120,
                              sorter: (a, b) => a.branch_code.localeCompare(b.branch_code),
                            },
                            {
                              title: 'Branch Name',
                              dataIndex: 'branch_name',
                              key: 'branch_name',
                              ellipsis: true,
                              sorter: (a, b) => a.branch_name.localeCompare(b.branch_name),
                            },
                            {
                              title: 'Qty Sold',
                              dataIndex: 'total_qty',  // Changed from total_qty_sold to total_qty
                              key: 'total_qty',  // Changed from total_qty_sold to total_qty
                              width: 100,
                              align: 'right',
                              sorter: (a, b) => a.total_qty - b.total_qty,  // Changed from total_qty_sold to total_qty
                              render: (value) => (
                                <Text strong style={{ color: '#1890ff' }}>
                                  {value.toLocaleString()}
                                </Text>
                              ),
                            },

                            {
                              title: 'First Sale',
                              dataIndex: 'first_sale_date',
                              key: 'first_sale_date',
                              width: 100,
                              sorter: (a, b) => (a.first_sale_date || '').localeCompare(b.first_sale_date || ''),
                              render: (value) => value || 'N/A',
                            },
                            {
                              title: 'Last Sale',
                              dataIndex: 'last_sale_date',
                              key: 'last_sale_date',
                              width: 100,
                              sorter: (a, b) => (a.last_sale_date || '').localeCompare(b.last_sale_date || ''),
                              render: (value) => value || 'N/A',
                            },

                            {
                              title: 'Total Sales (RM)',
                              dataIndex: 'total_sales',
                              key: 'total_sales',
                              width: 120,
                              align: 'right',
                              sorter: (a, b) => a.total_sales - b.total_sales,
                              render: (value) => (
                                <Text strong style={{ color: '#52c41a' }}>
                                  RM {value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                                </Text>
                              ),
                            },
                          ]}
                          dataSource={salesData.branch_details.filter(branch => 
                            !salesSearchText || 
                            branch.branch_code.toLowerCase().includes(salesSearchText.toLowerCase()) ||
                            branch.branch_name.toLowerCase().includes(salesSearchText.toLowerCase())
                          )}
                          rowKey="branch_code"
                          pagination={{
                            pageSize: 150,
                            showSizeChanger: true,
                            pageSizeOptions: ['50', '100', '150', '200', '500'],
                            showQuickJumper: true,
                            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} branches`,
                            size: 'small'
                          }}
                          scroll={{ y: 400 }}
                          size="small"
                          search={false}
                          toolBarRender={false}
                          options={false}
                        />
                      </div>
                    )}
                  </>
                ) : (
                  <Alert
                    message="No Sales Data"
                    description={salesData.message || 'No sales records found for this SKU.'}
                    type="info"
                    showIcon
                  />
                )}
              </>
            )}
          </Space>
        </Modal>

        {/* QOH Modal */}
        <QOHModal
          visible={qohModalVisible}
          onClose={handleCloseQOHModal}
          artno={qohSelectedSku?.Art_No}
          mcode={qohSelectedSku?.MCode}
          skuDescription={qohSelectedSku?.["ART_NO DESCRIPTION"]}
        />
      </PageContainer>
    </ConfigProvider>
  );
};

export default SkuCheckerPage; 