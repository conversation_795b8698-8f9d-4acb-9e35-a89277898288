#!/usr/bin/env python3
"""
Test script to verify the sales dates fix
"""
import requests
import json

def test_sales_dates():
    """Test the sales summary API to see if dates are returned"""
    
    # Test API endpoint
    url = "http://localhost:8011/api/tools/sku-checker/sales-summary"
    params = {
        "artno": "FM9932",
        "mcode": "9074514"
    }
    
    # You would need to add proper authentication headers here
    headers = {
        "Content-Type": "application/json"
    }
    
    print("=== Testing Sales Summary API ===")
    print(f"URL: {url}")
    print(f"Params: {params}")
    print()
    
    try:
        response = requests.get(url, params=params, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print("=== API Response ===")
            print(json.dumps(data, indent=2))
            
            print("\n=== Branch Details Check ===")
            branch_details = data.get('branch_details', [])
            if branch_details:
                for i, branch in enumerate(branch_details):
                    print(f"Branch {i+1}:")
                    print(f"  Code: {branch.get('branch_code')}")
                    print(f"  Name: {branch.get('branch_name')}")
                    print(f"  Total Qty: {branch.get('total_qty')}")
                    print(f"  Total Sales: {branch.get('total_sales')}")
                    print(f"  First Sale Date: {branch.get('first_sale_date')}")
                    print(f"  Last Sale Date: {branch.get('last_sale_date')}")
                    print()
                    
                    # Check if dates are present
                    if branch.get('first_sale_date') and branch.get('last_sale_date'):
                        print("✅ Dates are present!")
                    else:
                        print("❌ Dates are missing!")
            else:
                print("No branch details found!")
                
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_sales_dates() 