"""
SKU Checker Tool Logic

This module provides functions for the SKU Checker tool, allowing users to search for
SKU details and export the results to Excel. Optimized for large bulk searches.
"""
import logging
from typing import List, Dict, Optional, Any, Union
import mysql.connector
from decimal import Decimal
import xlsxwriter
import os
from datetime import datetime, date
import asyncio
import time

from ..models import SkuDetailItem
from ..utils.db_connections import get_mysql_connection, get_postgres_connection
from ..config import logger
from ..auth.dependencies import filter_cost_fields_from_data
# Optional import for image service (may not be available in all environments)
try:
    from ..services.sku_image_service import get_sku_images, get_batch_sku_images, get_image_url_for_api
    HAS_IMAGE_SERVICE = True
except ImportError:
    logger.warning("SKU Image Service not available - some features may be limited")
    HAS_IMAGE_SERVICE = False
    get_sku_images = None
    get_batch_sku_images = None
    get_image_url_for_api = None

def _chunk_list(lst: List[str], chunk_size: int) -> List[List[str]]:
    """Split a list into chunks of specified size."""
    for i in range(0, len(lst), chunk_size):
        yield lst[i:i + chunk_size]

def _build_optimized_query(
    codes: List[str], 
    field_name: str, 
    db_column: str, 
    use_wildcard: bool = False,
    chunk_size: int = 1000
) -> List[tuple]:
    """
    Build optimized query parts for large code lists.
    Returns list of (query_part, params) tuples.
    """
    query_parts = []
    
    if use_wildcard:
        # For wildcard searches, use smaller chunks to prevent query complexity
        chunk_size = min(chunk_size, 500)
        for chunk in _chunk_list(codes, chunk_size):
            like_conditions = [f"{db_column} LIKE %s" for _ in chunk]
            query_part = f"({' OR '.join(like_conditions)})"
            params = [f"%{code}%" for code in chunk]
            query_parts.append((query_part, params))
    else:
        # For exact matches, use chunked IN operations
        for chunk in _chunk_list(codes, chunk_size):
            if len(chunk) == 1:
                query_part = f"{db_column} = %s"
                params = chunk
            else:
                placeholders = ", ".join(["%s"] * len(chunk))
                query_part = f"{db_column} IN ({placeholders})"
                params = chunk
            query_parts.append((query_part, params))
    
    return query_parts

def fetch_sku_details(
    sku_item_code: Optional[str] = None,
    artno: Optional[str] = None,
    mcode: Optional[str] = None,
    description: Optional[str] = None,
    search_scope: Optional[str] = 'parent',
    use_wildcard: Optional[bool] = False,
    page_size: int = 10,
    page: int = 1,
    user_context: Optional[Dict[str, Any]] = None,
    max_total_results: int = 50000  # Maximum total results to prevent memory issues
) -> Dict[str, Any]:
    """
    Fetches SKU details from the armshq MySQL database based on specified search criteria.
    Optimized for large bulk searches with chunked processing and timeout protection.

    Args:
        sku_item_code (Optional[str]): The ARMSCode (SKU item code) to search for.
        artno (Optional[str]): The ArtNo (article number) to search for.
        mcode (Optional[str]): The MCode (manufacturer code) to search for.
        description (Optional[str]): The description to search for.
        search_scope (Optional[str]): 'parent' or 'all'. Defaults to 'parent'.
        use_wildcard (Optional[bool]): If True, uses LIKE '%value%' for code-based searches.
        page_size (int): The maximum number of records to return per page.
        page (int): The page number to fetch.
        user_context (Optional[Dict[str, Any]]): User context for filtering.
        max_total_results (int): Maximum total results to prevent memory issues.

    Returns:
        Dict[str, Any]: A dictionary containing data, counts, and any errors.
    """
    start_time = time.time()
    items = []
    
    select_fields = """SELECT
            si.sku_item_code,
            si.artno AS Art_No,
            si.mcode AS MCode,
            (SELECT si2.description FROM sku_items si2 WHERE si2.sku_id = si.sku_id AND si2.is_parent = '1' LIMIT 1) AS "ART_NO DESCRIPTION",
            b2.description AS Brand,
            c2.description AS Department,
            c3.description AS Category,
            c4.description AS Type_of_Silhouette,
            c5.description AS Silhouette,
            c6.description AS Gender,
            c7.description AS Age,
            si.color AS Color,
            si.size AS Size,
            v.description AS Vendor,
            (SELECT SUBSTRING_INDEX(SUBSTRING_INDEX(si_sub.additional_description, '"', -2), '"', 1) FROM sku_items si_sub WHERE si_sub.id = si.id) AS Season_Code,
            si.cost_price AS Cost_Price,
            si.hq_cost AS HQ_Cost,
            si.rsp_price AS RSP,
            si.rsp_discount AS RSP_Discount,
            si.selling_price AS Net_Selling_Price,
            CASE 
                WHEN s.active = '0' THEN 'Inactive'
                WHEN s.active = '1' THEN 'Active'
                ELSE 'Error'
            END AS Active,
            si.lastupdate,
            si.is_parent
    """

    from_join_clauses = """ FROM sku_items si
        INNER JOIN sku s ON s.id = si.sku_id
        INNER JOIN brand b2 ON b2.id = s.brand_id
        INNER JOIN category main_cat ON main_cat.id = s.category_id
        LEFT JOIN category_cache cc ON cc.category_id = main_cat.id
        LEFT JOIN category c2 ON cc.p2 = c2.id
        LEFT JOIN category c3 ON cc.p3 = c3.id
        LEFT JOIN category c4 ON cc.p4 = c4.id
        LEFT JOIN category c5 ON cc.p5 = c5.id
        LEFT JOIN category c6 ON cc.p6 = c6.id
        LEFT JOIN category c7 ON cc.p7 = c7.id
        LEFT JOIN vendor v ON v.id = s.vendor_id
    """

    # Build conditions with optimized chunking for large datasets
    conditions = []
    all_params_main = []
    all_params_count = []
    search_field_used = None
    total_codes_count = 0

    param_map = {
        "sku_item_code": sku_item_code,
        "artno": artno,
        "mcode": mcode
    }

    db_field_map = {
        "sku_item_code": "si.sku_item_code",
        "artno": "si.artno",
        "mcode": "si.mcode"
    }

    # Process code-based searches with enhanced chunking
    for field_name, input_value in param_map.items():
        if input_value:
            search_field_used = field_name
            codes = [code.strip() for code in input_value.split('\n') if code.strip()]
            
            if not codes:
                continue

            total_codes_count += len(codes)
            db_column = db_field_map[field_name]
            
            # Performance warning for very large datasets
            if len(codes) > 10000:
                logger.warning(f"SKU Checker: Very large number of {field_name} codes ({len(codes)}). This may cause performance issues.")
            
            # Use enhanced chunked query building for large datasets
            if len(codes) > 500:  # Use chunked approach for large lists
                # Smaller chunks for wildcard searches to prevent query complexity
                chunk_size = 800 if not use_wildcard else 300
                query_parts = _build_optimized_query(codes, field_name, db_column, use_wildcard, chunk_size)
                
                # Limit number of chunks to prevent query from becoming too complex
                max_chunks = 20  # Prevent queries with too many OR conditions
                if len(query_parts) > max_chunks:
                    logger.warning(f"SKU Checker: Query has {len(query_parts)} chunks, limiting to {max_chunks} for performance")
                    query_parts = query_parts[:max_chunks]
                    # Inform user about limitation
                    limited_codes = sum(len(params) for _, params in query_parts)
                    logger.info(f"SKU Checker: Limited search to first {limited_codes} codes out of {len(codes)} for performance")
                
                # Combine all chunks with OR
                chunk_conditions = []
                for query_part, params in query_parts:
                    chunk_conditions.append(query_part)
                    all_params_main.extend(params)
                    all_params_count.extend(params)
                
                if chunk_conditions:
                    conditions.append(f"({' OR '.join(chunk_conditions)})")
            else:
                # Use original logic for smaller lists
                if use_wildcard:
                    like_conditions = [f"{db_column} LIKE %s" for _ in codes]
                    conditions.append(f"({' OR '.join(like_conditions)})")
                    for code in codes:
                        param_value = f"%{code}%"
                        all_params_main.append(param_value)
                        all_params_count.append(param_value)
                else:
                    if len(codes) == 1:
                        conditions.append(f"{db_column} = %s")
                        all_params_main.append(codes[0])
                        all_params_count.append(codes[0])
                    else:
                        placeholders = ", ".join(["%s"] * len(codes))
                        conditions.append(f"{db_column} IN ({placeholders})")
                        all_params_main.extend(codes)
                        all_params_count.extend(codes)

    # Handle description search
    if description:
        if not search_field_used:
            search_field_used = "description"
        conditions.append("(si.description LIKE %s OR EXISTS (SELECT 1 FROM sku_items parent_desc_check WHERE parent_desc_check.sku_id = si.sku_id AND parent_desc_check.is_parent = '1' AND parent_desc_check.description LIKE %s))")
        param_value_desc = f"%{description}%"
        all_params_main.append(param_value_desc)
        all_params_main.append(param_value_desc)
        all_params_count.append(param_value_desc)
        all_params_count.append(param_value_desc)

    if not conditions:
        logger.info("fetch_sku_details called without primary search criteria.")
        return {"data": [], "current_batch_count": 0, "grand_total": 0, "error": "Please provide ARMSCode, ArtNo, MCode, or Description to search."}

    where_clause_str = " WHERE " + " AND ".join(conditions)
    
    if search_scope == 'parent':
        where_clause_str += " AND si.is_parent = '1'"

    conn = None
    cursor = None
    grand_total_count = 0
    
    try:
        conn = get_mysql_connection()
        if conn is None:
            logger.error("Failed to get MySQL connection for SKU checker.")
            return {"data": [], "current_batch_count": 0, "grand_total": 0, "error": "Database connection error."}
        
        cursor = conn.cursor(dictionary=True)
        
        # Set extended timeouts for large queries
        cursor.execute("SET SESSION wait_timeout = 600")  # 10 minutes
        cursor.execute("SET SESSION interactive_timeout = 600")
        cursor.execute("SET SESSION net_read_timeout = 300")  # 5 minutes
        cursor.execute("SET SESSION net_write_timeout = 300")
        
        # Execute count query first with timeout protection
        count_query = "SELECT COUNT(*) as total_count" + from_join_clauses + where_clause_str
        
        logger.info(f"SKU Checker: Executing COUNT query with {len(all_params_count)} parameters for {total_codes_count} total codes")
        start_count_time = time.time()
        
        try:
            cursor.execute(count_query, tuple(all_params_count))
            count_result = cursor.fetchone()
            if count_result:
                grand_total_count = count_result['total_count']
        except mysql.connector.Error as count_err:
            if "timeout" in str(count_err).lower() or "lock wait timeout" in str(count_err).lower():
                logger.error(f"Count query timeout: {count_err}")
                return {
                    "data": [],
                    "current_batch_count": 0,
                    "grand_total": 0,
                    "error": f"Query timeout while counting results. Please reduce the number of search codes (currently {total_codes_count}) or refine your search criteria."
                }
            raise count_err
        
        count_time = time.time() - start_count_time
        logger.info(f"Count query completed in {count_time:.2f}s. Total records: {grand_total_count}")
        
        # Check if total results exceed maximum
        if grand_total_count > max_total_results:
            logger.warning(f"SKU Checker: Query returned {grand_total_count} results, exceeding maximum of {max_total_results}")
            return {
                "data": [],
                "current_batch_count": 0,
                "grand_total": grand_total_count,
                "error": f"Query returned too many results ({grand_total_count:,}). Please refine your search criteria. Maximum allowed: {max_total_results:,}. Consider using more specific search terms or reducing the number of codes."
            }
        
        # Execute main data query with pagination
        offset = (page - 1) * page_size
        full_query_main = select_fields + from_join_clauses + where_clause_str + f" ORDER BY si.artno, si.mcode LIMIT %s OFFSET %s"
        query_params_main_paginated = list(all_params_main)
        query_params_main_paginated.append(page_size)
        query_params_main_paginated.append(offset)

        logger.info(f"SKU Checker: Executing MAIN query with {len(query_params_main_paginated)} parameters, page {page}, size {page_size}")
        start_main_time = time.time()
        
        try:
            cursor.execute(full_query_main, tuple(query_params_main_paginated))
            results = cursor.fetchall()
        except mysql.connector.Error as main_err:
            if "timeout" in str(main_err).lower() or "lock wait timeout" in str(main_err).lower():
                logger.error(f"Main query timeout: {main_err}")
                return {
                    "data": [],
                    "current_batch_count": 0,
                    "grand_total": grand_total_count,
                    "error": f"Query timeout while fetching data. Please reduce the number of search codes (currently {total_codes_count}) or use smaller page sizes."
                }
            raise main_err
        
        main_time = time.time() - start_main_time
        logger.info(f"Main query completed in {main_time:.2f}s. Retrieved {len(results)} records")
        
        # Batch fetch images for all SKUs to reduce database connections
        artnos = [row.get('Art_No') for row in results if row.get('Art_No')]
        batch_images = {}
        if artnos and HAS_IMAGE_SERVICE:
            try:
                batch_images = get_batch_sku_images(artnos)
            except Exception as img_err:
                logger.warning(f"Failed to fetch batch images: {img_err}")
        elif artnos and not HAS_IMAGE_SERVICE:
            logger.debug("SKU Image Service not available - skipping image fetching")
        
        parsed_items = []
        for row in results:
            if 'is_parent' in row and row['is_parent'] is not None:
                row['is_parent'] = str(row['is_parent'])
            
            try:
                # Get images for this SKU using batch results
                artno = row.get('Art_No')
                if artno and artno in batch_images:
                    sku_images_response = batch_images[artno]
                    if sku_images_response.success:
                        # Images already have proper structure with filename and image_url
                        row['images'] = [img.model_dump() for img in sku_images_response.images]
                        row['image_count'] = sku_images_response.total_count
                    else:
                        row['images'] = []
                        row['image_count'] = 0
                else:
                    row['images'] = []
                    row['image_count'] = 0
                
                parsed_items.append(SkuDetailItem(**row))
            except Exception as pydantic_err:
                logger.error(f"Pydantic validation error for SKU row: {row}. Error: {pydantic_err}")
        
        # Apply cost field filtering if user context is provided
        if user_context:
            # Convert Pydantic models back to dicts using by_alias=True to preserve field names
            data_dicts = [item.model_dump(by_alias=True) for item in parsed_items]
            # Filter cost fields from the data using the standard function
            filtered_data_dicts = filter_cost_fields_from_data(data_dicts, user_context)
            # Convert back to Pydantic models
            filtered_items = []
            for item_dict in filtered_data_dicts:
                try:
                    filtered_items.append(SkuDetailItem(**item_dict))
                except Exception as pydantic_err:
                    logger.error(f"Pydantic validation error after cost filtering: {pydantic_err}")
                    logger.error(f"Item dict that failed validation: {item_dict}")
            
            total_time = time.time() - start_time
            logger.info(f"SKU Checker search completed in {total_time:.2f}s total")
            
            return {"data": filtered_items, "current_batch_count": len(filtered_items), "grand_total": grand_total_count, "error": None}
        
        total_time = time.time() - start_time
        logger.info(f"SKU Checker search completed in {total_time:.2f}s total")
        
        return {"data": parsed_items, "current_batch_count": len(parsed_items), "grand_total": grand_total_count, "error": None}

    except mysql.connector.Error as err:
        logger.error(f"MySQL Error in fetch_sku_details: {err}")
        if "timeout" in str(err).lower() or "lock wait timeout" in str(err).lower():
            return {
                "data": [], 
                "current_batch_count": 0, 
                "grand_total": 0, 
                "error": f"Database timeout occurred. Please reduce the number of search codes (currently {total_codes_count}) or refine your search criteria to get faster results."
            }
        return {"data": [], "current_batch_count": 0, "grand_total": 0, "error": f"Database query failed: {err}"}
    except Exception as e:
        logger.error(f"Unexpected error in fetch_sku_details: {e}")
        return {"data": [], "current_batch_count": 0, "grand_total": 0, "error": f"An unexpected error occurred: {e}"}
    finally:
        if cursor:
            cursor.close()
        if conn and conn.is_connected():
            conn.close()


def fetch_all_sku_details_for_export(
    sku_item_code: Optional[str] = None,
    artno: Optional[str] = None,
    mcode: Optional[str] = None,
    description: Optional[str] = None,
    search_scope: Optional[str] = 'parent',
    use_wildcard: Optional[bool] = False,
    user_context: Optional[Dict[str, Any]] = None
) -> List[Dict[str, Any]]:
    """
    Fetches ALL SKU details matching the criteria for export (no pagination).
    Optimized for large bulk searches with enhanced chunking and timeout protection.
    
    Returns:
        List[Dict[str, Any]]: List of all matching SKU records as dictionaries.
    """
    start_time = time.time()
    query_params = []
    total_codes_count = 0
    
    select_fields = """SELECT
            si.sku_item_code,
            si.artno AS Art_No,
            si.mcode AS MCode,
            (SELECT si2.description FROM sku_items si2 WHERE si2.sku_id = si.sku_id AND si2.is_parent = '1' LIMIT 1) AS "ART_NO DESCRIPTION",
            b2.description AS Brand,
            c2.description AS Department,
            c3.description AS Category,
            c4.description AS Type_of_Silhouette,
            c5.description AS Silhouette,
            c6.description AS Gender,
            c7.description AS Age,
            si.color AS Color,
            si.size AS Size,
            v.description AS Vendor,
            (SELECT SUBSTRING_INDEX(SUBSTRING_INDEX(si_sub.additional_description, '"', -2), '"', 1) FROM sku_items si_sub WHERE si_sub.id = si.id) AS Season_Code,
            si.cost_price AS Cost_Price,
            si.hq_cost AS HQ_Cost,
            si.rsp_price AS RSP,
            si.rsp_discount AS RSP_Discount,
            si.selling_price AS Net_Selling_Price,
            CASE 
                WHEN s.active = '0' THEN 'Inactive'
                WHEN s.active = '1' THEN 'Active'
                ELSE 'Error'
            END AS Active,
            si.lastupdate,
            si.is_parent
    """

    conditions = []
    search_field_used = None

    param_map = {
        "sku_item_code": sku_item_code,
        "artno": artno,
        "mcode": mcode
    }

    db_field_map = {
        "sku_item_code": "si.sku_item_code",
        "artno": "si.artno",
        "mcode": "si.mcode"
    }

    # Process code-based searches with enhanced chunking for export
    for field_name, input_value in param_map.items():
        if input_value:
            search_field_used = field_name
            codes = [code.strip() for code in input_value.split('\n') if code.strip()]
            
            if not codes:
                continue

            total_codes_count += len(codes)
            db_column = db_field_map[field_name]

            # Use same chunking logic as main fetch function for consistency
            if len(codes) > 500:  # Use chunked approach for large lists
                chunk_size = 800 if not use_wildcard else 300
                query_parts = _build_optimized_query(codes, field_name, db_column, use_wildcard, chunk_size)
                
                # Limit number of chunks for export as well
                max_chunks = 25  # Slightly higher limit for export
                if len(query_parts) > max_chunks:
                    logger.warning(f"SKU Export: Query has {len(query_parts)} chunks, limiting to {max_chunks} for performance")
                    query_parts = query_parts[:max_chunks]
                    limited_codes = sum(len(params) for _, params in query_parts)
                    logger.info(f"SKU Export: Limited search to first {limited_codes} codes out of {len(codes)} for performance")
                
                # Combine all chunks with OR
                chunk_conditions = []
                for query_part, params in query_parts:
                    chunk_conditions.append(query_part)
                    query_params.extend(params)
                
                if chunk_conditions:
                    conditions.append(f"({' OR '.join(chunk_conditions)})")
            else:
                # Use original logic for smaller lists
                if use_wildcard:
                    like_conditions = [f"{db_column} LIKE %s" for _ in codes]
                    conditions.append(f"({' OR '.join(like_conditions)})")
                    for code in codes:
                        param_value = f"%{code}%"
                        query_params.append(param_value)
                else:
                    if len(codes) == 1:
                        conditions.append(f"{db_column} = %s")
                        query_params.append(codes[0])
                    else:
                        placeholders = ", ".join(["%s"] * len(codes))
                        conditions.append(f"{db_column} IN ({placeholders})")
                        query_params.extend(codes)

    if description:
        if not search_field_used:
             search_field_used = "description"
        conditions.append("(si.description LIKE %s OR EXISTS (SELECT 1 FROM sku_items parent_desc_check WHERE parent_desc_check.sku_id = si.sku_id AND parent_desc_check.is_parent = '1' AND parent_desc_check.description LIKE %s))")
        param_value_desc = f"%{description}%"
        query_params.append(param_value_desc)
        query_params.append(param_value_desc)

    if not conditions:
        logger.warning("fetch_all_sku_details_for_export called without search criteria.")
        return []

    where_clause_str = " WHERE " + " AND ".join(conditions)
    
    if search_scope == 'parent':
        where_clause_str += " AND si.is_parent = '1'"

    from_join_clauses = """ FROM sku_items si
        INNER JOIN sku s ON s.id = si.sku_id
        INNER JOIN brand b2 ON b2.id = s.brand_id
        INNER JOIN category main_cat ON main_cat.id = s.category_id
        LEFT JOIN category_cache cc ON cc.category_id = main_cat.id
        LEFT JOIN category c2 ON cc.p2 = c2.id
        LEFT JOIN category c3 ON cc.p3 = c3.id
        LEFT JOIN category c4 ON cc.p4 = c4.id
        LEFT JOIN category c5 ON cc.p5 = c5.id
        LEFT JOIN category c6 ON cc.p6 = c6.id
        LEFT JOIN category c7 ON cc.p7 = c7.id
        LEFT JOIN vendor v ON v.id = s.vendor_id
    """
    
    full_query = select_fields + from_join_clauses + where_clause_str + " ORDER BY si.artno, si.mcode"
    
    conn = None
    cursor = None
    try:
        conn = get_mysql_connection()
        if conn is None:
            logger.error("Failed to get MySQL connection for SKU export.")
            return []
        
        cursor = conn.cursor(dictionary=True)
        
        # Set extended timeouts for export queries
        cursor.execute("SET SESSION wait_timeout = 900")  # 15 minutes for export
        cursor.execute("SET SESSION interactive_timeout = 900")
        cursor.execute("SET SESSION net_read_timeout = 600")  # 10 minutes
        cursor.execute("SET SESSION net_write_timeout = 600")
        
        logger.info(f"SKU Export: Executing query with {len(query_params)} parameters for {total_codes_count} total codes")
        start_query_time = time.time()
        
        cursor.execute(full_query, tuple(query_params))
        results = cursor.fetchall()
        
        query_time = time.time() - start_query_time
        logger.info(f"SKU Export: Query completed in {query_time:.2f}s. Retrieved {len(results)} records")
        
        # Apply cost field filtering if user context is provided
        if user_context:
            # Convert results to list of dicts and apply standard cost filtering
            results_dicts = [dict(row) for row in results]
            filtered_results = filter_cost_fields_from_data(results_dicts, user_context)
            
            total_time = time.time() - start_time
            logger.info(f"SKU Export: Completed with cost filtering in {total_time:.2f}s total")
            return filtered_results
        
        total_time = time.time() - start_time
        logger.info(f"SKU Export: Completed in {total_time:.2f}s total")
        return results

    except mysql.connector.Error as err:
        logger.error(f"MySQL Error in fetch_all_sku_details_for_export: {err}")
        if "timeout" in str(err).lower() or "lock wait timeout" in str(err).lower():
            logger.error(f"Export query timeout with {total_codes_count} codes")
        return []
    except Exception as e:
        logger.error(f"Unexpected error in fetch_all_sku_details_for_export: {e}")
        return []
    finally:
        if cursor:
            cursor.close()
        if conn and conn.is_connected():
            conn.close()


def export_sku_details_to_excel(
    filters: Dict[str, Any],
    export_folder: str,
    export_id: int,
    user_context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Exports SKU details to Excel based on the provided filters.
    
    Args:
        filters (Dict[str, Any]): Search filters for SKU details
        export_folder (str): Directory to save the Excel file
        export_id (int): Unique export ID for filename
        
    Returns:
        Dict[str, Any]: Result with success status, filename, and any errors
    """
    try:
        # Extract filters
        sku_item_code = filters.get('sku_item_code')
        artno = filters.get('artno')
        mcode = filters.get('mcode')
        description = filters.get('description')
        search_scope = filters.get('search_scope', 'parent')
        use_wildcard = filters.get('use_wildcard', False)
        
        # Fetch all data for export
        sku_data = fetch_all_sku_details_for_export(
            sku_item_code=sku_item_code,
            artno=artno,
            mcode=mcode,
            description=description,
            search_scope=search_scope,
            use_wildcard=use_wildcard,
            user_context=user_context
        )
        
        if not sku_data:
            return {
                "success": False,
                "error": "No SKU data found matching the criteria for export."
            }
        
        # Generate filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"sku_details_export_{export_id}_{timestamp}.xlsx"
        file_path = os.path.join(export_folder, filename)
        
        # Ensure export directory exists
        os.makedirs(export_folder, exist_ok=True)
        
        # Create Excel file
        workbook = xlsxwriter.Workbook(file_path)
        worksheet = workbook.add_worksheet('SKU Details')
        
        # Define formats
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#D7E4BC',
            'border': 1,
            'align': 'center',
            'valign': 'vcenter'
        })
        
        cell_format = workbook.add_format({
            'border': 1,
            'align': 'left',
            'valign': 'vcenter'
        })
        
        number_format = workbook.add_format({
            'border': 1,
            'align': 'right',
            'valign': 'vcenter',
            'num_format': '#,##0.00'
        })
        
        date_format = workbook.add_format({
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
            'num_format': 'yyyy-mm-dd hh:mm:ss'
        })
        
        # Define headers based on cost permission
        has_cost_permission = user_context.get('has_cost_permission', True) if user_context else True
        
        base_headers = [
            'Status', 'ARMSCode', 'MCode', 'Art No', 'ART_NO DESCRIPTION',
            'Brand', 'Department', 'Category', 'Type_of_Silhouette', 'Silhouette',
            'Gender', 'Age', 'Color', 'Size', 'Vendor', 'Season_Code'
        ]
        
        cost_headers = ['Cost_Price', 'HQ_Cost'] if has_cost_permission else []
        
        end_headers = ['RSP', 'RSP_Discount', 'Net_Selling_Price', 'Last Update', 'Is Parent']
        
        headers = base_headers + cost_headers + end_headers
        
        # Write headers
        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)
        
        # Write data
        for row_idx, sku in enumerate(sku_data, start=1):
            col = 0
            
            # Status
            worksheet.write(row_idx, col, sku.get('Active', ''), cell_format)
            col += 1
            
            # ARMSCode
            worksheet.write(row_idx, col, sku.get('sku_item_code', ''), cell_format)
            col += 1
            
            # MCode
            worksheet.write(row_idx, col, sku.get('MCode', ''), cell_format)
            col += 1
            
            # Art No
            worksheet.write(row_idx, col, sku.get('Art_No', ''), cell_format)
            col += 1
            
            # ART_NO DESCRIPTION
            worksheet.write(row_idx, col, sku.get('ART_NO DESCRIPTION', ''), cell_format)
            col += 1
            
            # Brand
            worksheet.write(row_idx, col, sku.get('Brand', ''), cell_format)
            col += 1
            
            # Department
            worksheet.write(row_idx, col, sku.get('Department', ''), cell_format)
            col += 1
            
            # Category
            worksheet.write(row_idx, col, sku.get('Category', ''), cell_format)
            col += 1
            
            # Type_of_Silhouette
            worksheet.write(row_idx, col, sku.get('Type_of_Silhouette', ''), cell_format)
            col += 1
            
            # Silhouette
            worksheet.write(row_idx, col, sku.get('Silhouette', ''), cell_format)
            col += 1
            
            # Gender
            worksheet.write(row_idx, col, sku.get('Gender', ''), cell_format)
            col += 1
            
            # Age
            worksheet.write(row_idx, col, sku.get('Age', ''), cell_format)
            col += 1
            
            # Color
            worksheet.write(row_idx, col, sku.get('Color', ''), cell_format)
            col += 1
            
            # Size
            worksheet.write(row_idx, col, sku.get('Size', ''), cell_format)
            col += 1
            
            # Vendor
            worksheet.write(row_idx, col, sku.get('Vendor', ''), cell_format)
            col += 1
            
            # Season_Code
            worksheet.write(row_idx, col, sku.get('Season_Code', ''), cell_format)
            col += 1
            
            # Cost fields (only if user has permission)
            if has_cost_permission:
                # Cost_Price
                cost_price = sku.get('Cost_Price')
                if cost_price is not None:
                    worksheet.write_number(row_idx, col, float(cost_price), number_format)
                else:
                    worksheet.write(row_idx, col, '', cell_format)
                col += 1
                
                # HQ_Cost
                hq_cost = sku.get('HQ_Cost')
                if hq_cost is not None:
                    worksheet.write_number(row_idx, col, float(hq_cost), number_format)
                else:
                    worksheet.write(row_idx, col, '', cell_format)
                col += 1
            
            # RSP
            rsp = sku.get('RSP')
            if rsp is not None:
                worksheet.write_number(row_idx, col, float(rsp), number_format)
            else:
                worksheet.write(row_idx, col, '', cell_format)
            col += 1
            
            # RSP_Discount
            worksheet.write(row_idx, col, sku.get('RSP_Discount', ''), cell_format)
            col += 1
            
            # Net_Selling_Price
            net_selling_price = sku.get('Net_Selling_Price')
            if net_selling_price is not None:
                worksheet.write_number(row_idx, col, float(net_selling_price), number_format)
            else:
                worksheet.write(row_idx, col, '', cell_format)
            col += 1
            
            # Last Update
            last_update = sku.get('lastupdate')
            if last_update:
                if isinstance(last_update, str):
                    worksheet.write(row_idx, col, last_update, cell_format)
                else:
                    # Remove timezone information if present (Excel doesn't support timezones)
                    if isinstance(last_update, datetime) and last_update.tzinfo is not None:
                        last_update = last_update.replace(tzinfo=None)
                    worksheet.write_datetime(row_idx, col, last_update, date_format)
            else:
                worksheet.write(row_idx, col, '', cell_format)
            col += 1
            
            # Is Parent
            is_parent = sku.get('is_parent', '')
            is_parent_display = 'Yes' if str(is_parent) == '1' else 'No' if str(is_parent) == '0' else ''
            worksheet.write(row_idx, col, is_parent_display, cell_format)
        
        # Auto-adjust column widths
        for col, header in enumerate(headers):
            if header in ['ART_NO DESCRIPTION', 'Type_of_Silhouette']:
                worksheet.set_column(col, col, 25)
            elif header in ['ARMSCode', 'MCode', 'Art No', 'Brand', 'Department', 'Category']:
                worksheet.set_column(col, col, 15)
            elif header in ['Cost_Price', 'HQ_Cost', 'RSP', 'Net_Selling_Price']:
                worksheet.set_column(col, col, 12)
            elif header == 'Last Update':
                worksheet.set_column(col, col, 18)
            else:
                worksheet.set_column(col, col, 10)
        
        workbook.close()
        
        logger.info(f"SKU details export completed successfully. File: {filename}")
        
        return {
            "success": True,
            "filename": filename,
            "file_path": file_path,
            "records_exported": len(sku_data)
        }
        
    except Exception as e:
        logger.error(f"Error exporting SKU details to Excel: {e}")
        return {
            "success": False,
            "error": f"Export failed: {str(e)}"
        } 

def get_sku_sales_summary(
    artno: str, 
    mcode: Optional[str] = None, 
    user_context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Retrieves sales summary for SKU. Handles both parent codes (artno only) and child codes (artno + mcode).
    For parent codes: Sums sales across all child MCodes
    For child codes: Shows sales for specific artno + mcode combination
    
    Args:
        artno (str): The article number to search for
        mcode (Optional[str]): The manufacturer code - if None, treats as parent code search
        user_context (Optional[Dict[str, Any]]): User context for branch filtering
        
    Returns:
        Dict[str, Any]: Sales summary with total quantity sold and branch breakdown
    """
    conn = None
    cursor = None
    
    try:
        conn = get_postgres_connection()
        if conn is None:
            logger.error("Failed to get PostgreSQL connection for SKU sales summary.")
            return {
                "artno": artno,
                "mcode": mcode or "ALL_CHILDREN",
                "total_qty_sold": 0,
                "total_sales_amount": 0,  # Add total sales amount
                "branch_count": 0,
                "branch_details": [],
                "success": False,
                "message": "Database connection error."
            }
        
        cursor = conn.cursor()
        
        # Build query based on whether we have mcode (child) or not (parent)
        if mcode:
            # Child code search - specific artno + mcode
            base_query = """
                SELECT 
                    branch_code,
                    branch_name,
                    SUM(qty_sold) as total_qty_sold,
                    SUM(net_selling_price) as total_sales,
                    MIN(date) as earliest_sale_date,
                    MAX(date) as latest_sale_date
                FROM salesbysku 
                WHERE art_no = %s AND mcode = %s
            """
            params = [artno, mcode]
            search_type = "specific SKU (Art No + MCode)"
        else:
            # Parent code search - all MCodes for this artno
            base_query = """
                SELECT 
                    branch_code,
                    branch_name,
                    SUM(qty_sold) as total_qty_sold,
                    SUM(net_selling_price) as total_sales,
                    MIN(date) as earliest_sale_date,
                    MAX(date) as latest_sale_date
                FROM salesbysku 
                WHERE art_no = %s
            """
            params = [artno]
            search_type = "parent code (all child MCodes)"
        
        # Apply branch filtering for non-admin users
        if user_context and not user_context.get('is_admin', False):
            permitted_branches = user_context.get('permitted_branches', [])
            if permitted_branches:
                placeholders = ', '.join(['%s'] * len(permitted_branches))
                base_query += f" AND branch_code IN ({placeholders})"
                params.extend(permitted_branches)
            else:
                # No permitted branches - return empty result
                return {
                    "artno": artno,
                    "mcode": mcode or "ALL_CHILDREN",
                    "total_qty_sold": 0,
                    "total_sales_amount": 0,  # Add total sales amount
                    "branch_count": 0,
                    "branch_details": [],
                    "success": True,
                    "message": "No sales data available for your permitted branches."
                }
        
        base_query += """
            GROUP BY branch_code, branch_name
            ORDER BY total_qty_sold DESC, branch_code
        """
        
        logger.info(f"Executing sales summary query for {search_type}: artno={artno}, mcode={mcode or 'ALL'}")
        cursor.execute(base_query, params)
        results = cursor.fetchall()
        
        # Process results
        branch_details = []
        total_qty_sold = 0
        total_sales_amount = 0  # Add total sales tracking
        earliest_date = None
        latest_date = None
        
        for row in results:
            branch_code, branch_name, qty_sold, total_sales, earliest_sale, latest_sale = row
            
            total_qty_sold += int(qty_sold or 0)  # Convert to int
            total_sales_amount += float(total_sales or 0)  # Keep as float for main total
            
            if earliest_sale:
                if earliest_date is None or earliest_sale < earliest_date:
                    earliest_date = earliest_sale
            
            if latest_sale:
                if latest_date is None or latest_sale > latest_date:
                    latest_date = latest_sale
            
            branch_details.append({
                "branch_code": branch_code,
                "branch_name": branch_name,
                "total_qty": int(qty_sold or 0),  # Convert to int
                "total_sales": float(total_sales or 0),  # Convert to float instead of Decimal
                "first_sale_date": earliest_sale.strftime('%Y-%m-%d') if earliest_sale else None,
                "last_sale_date": latest_sale.strftime('%Y-%m-%d') if latest_sale else None
            })
        
        logger.info(f"Sales summary completed. Total qty sold: {total_qty_sold}, Total sales: {total_sales_amount} across {len(branch_details)} branches")
        
        # Create appropriate message based on search type
        if mcode:
            message = f"Found sales data for specific SKU across {len(branch_details)} branches."
        else:
            message = f"Found sales data for parent code (all child variants) across {len(branch_details)} branches."
        
        return {
            "artno": artno,
            "mcode": mcode or "ALL_CHILDREN",
            "total_qty_sold": total_qty_sold,
            "total_sales_amount": total_sales_amount,  # Add total sales to response
            "branch_count": len(branch_details),
            "first_sale_date": earliest_date.strftime('%Y-%m-%d') if earliest_date else None,
            "last_sale_date": latest_date.strftime('%Y-%m-%d') if latest_date else None,
            "branch_details": branch_details,
            "success": True,
            "message": message
        }
        
    except Exception as e:
        logger.error(f"Error retrieving sales summary for artno {artno}, mcode {mcode}: {e}")
        return {
            "artno": artno,
            "mcode": mcode or "ALL_CHILDREN",
            "total_qty_sold": 0,
            "total_sales_amount": 0,  # Add total sales amount
            "branch_count": 0,
            "branch_details": [],
            "success": False,
            "message": f"Failed to retrieve sales data: {str(e)}"
        }
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close() 