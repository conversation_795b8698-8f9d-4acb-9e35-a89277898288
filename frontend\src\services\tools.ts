import { request } from '@umijs/max';

// Matches backend/src/models/__init__.py SkuDetailItem
export interface SkuDetailItem {
  sku_item_code: string;
  Art_No?: string;
  MCode?: string;
  'ART_NO DESCRIPTION'?: string;
  Brand?: string;
  Department?: string;
  Category?: string;
  Type_of_Silhouette?: string;
  Silhouette?: string;
  Gender?: string;
  Age?: string;
  Color?: string;
  Size?: string;
  Vendor?: string;
  Season_Code?: string;
  Cost_Price?: number;
  HQ_Cost?: number;
  RSP?: number;
  RSP_Discount?: string;
  Net_Selling_Price?: number;
  Active: string;
  lastupdate?: string;
  is_parent?: string;
  images?: SkuImageItem[];
  image_count?: number;
}

// SKU Image related types
export interface SkuImageItem {
  id: number;
  artno: string;
  filename: string;
  local_path: string;
  original_url?: string;
  created_at: string;
  image_url: string; // API URL for accessing the image
}

export interface SkuImagesListResponse {
  success: boolean;
  images: SkuImageItem[];
  total_count: number;
  message?: string;
}

export interface GoogleImageSearchRequest {
  artno: string;
  search_query: string;
  num_results?: number;
}

export interface GoogleImageResult {
  title: string;
  link: string;
  thumbnail: string;
  displayLink: string;
  snippet: string;
}

export interface GoogleImageSearchResponse {
  success: boolean;
  results: GoogleImageResult[];
  total_results: number;
  message?: string;
}

export interface SkuImageSaveRequest {
  artno: string;
  image_url: string;
  image_title?: string;
  remove_background?: boolean;
}

export interface SkuImageUploadRequest {
  artno: string;
  image_data: string; // base64 encoded image data
  filename: string;
  remove_background?: boolean;
}

export interface BulkImageUploadItem {
  artno: string;
  image_data: string; // base64 encoded image data
  filename?: string;
  remove_background?: boolean;
}

export interface BulkImageUploadRequest {
  images: BulkImageUploadItem[];
}

export interface BulkImageUploadResult {
  artno: string;
  filename?: string;
  success: boolean;
  message: string;
  image_id?: number;
  local_path?: string;
  error?: string;
}

export interface BulkImageUploadResponse {
  total_requested: number;
  successful_uploads: number;
  failed_uploads: number;
  results: BulkImageUploadResult[];
  success: boolean;
  message: string;
}

export interface SkuImageResponse {
  success: boolean;
  message: string;
  filename?: string;
  local_path?: string;
}

// Matches backend/src/models/__init__.py FetchSkuDetailsResponse
export interface FetchSkuDetailsResponse {
  data: SkuDetailItem[];
  current_batch_count: number;
  grand_total: number;
  success: boolean;
  error?: string;
  // New fields for handling large result sets
  is_result_limited?: boolean;
  result_limit_message?: string;
  export_recommended?: boolean;
}

// SKU Sales Summary types
export interface SkuSalesBranchDetail {
  branch_code: string;
  branch_name: string;
  total_qty: number;  // Changed from total_qty_sold to total_qty
  total_sales: number;  // Added total_sales field
  transaction_count?: number;  // Made optional since backend doesn't provide this
  first_sale_date?: string;
  last_sale_date?: string;
}

export interface SkuSalesSummaryResponse {
  artno: string;
  mcode?: string; // Optional - will be undefined for parent code searches
  total_qty_sold: number;
  total_sales_amount?: number;  // Added total_sales_amount field
  branch_count: number;
  first_sale_date?: string;
  last_sale_date?: string;
  branch_details: SkuSalesBranchDetail[];
  success: boolean;
  message?: string;
}

export interface FetchSkuDetailsParams {
  sku_item_code?: string;
  artno?: string;
  mcode?: string;
  description?: string;
  search_scope?: string;
  use_wildcard?: boolean;
  page_size?: number;
  page?: number;
}

/**
 * Fetches SKU sales summary from the backend API.
 * GET /api/tools/sku-checker/sales-summary
 */
export async function fetchSkuSalesSummary(artno: string, mcode?: string): Promise<SkuSalesSummaryResponse> {
  try {
    // Build params object, only include mcode if provided
    const params: any = { artno };
    if (mcode) {
      params.mcode = mcode;
    }
    
    const response = await request<SkuSalesSummaryResponse>('/api/tools/sku-checker/sales-summary', {
      method: 'GET',
      params,
    });
    
    if (typeof response.success === 'undefined') {
      console.error('fetchSkuSalesSummary: API response missing success field', response);
      return {
        artno,
        mcode,
        total_qty_sold: 0,
        total_sales_amount: 0,  // Added total_sales_amount field
        branch_count: 0,
        branch_details: [],
        success: false,
        message: 'Invalid response format from server.',
      };
    }
    return response;
  } catch (error: any) {
    console.error('fetchSkuSalesSummary error:', error);
    let errorMessage = 'Failed to fetch sales summary from server.';
    if (error?.response?.data?.detail) {
      if (typeof error.response.data.detail === 'string') {
        errorMessage = error.response.data.detail;
      }
    } else if (error?.message) {
      errorMessage = error.message;
    }

    return {
      artno,
      mcode,
      total_qty_sold: 0,
      total_sales_amount: 0,  // Added total_sales_amount field
      branch_count: 0,
      branch_details: [],
      success: false,
      message: errorMessage,
    };
  }
}

/**
 * Fetches SKU details from the backend API.
 * GET /api/tools/sku-checker
 */
export async function fetchSkuDetails(params: FetchSkuDetailsParams): Promise<FetchSkuDetailsResponse> {
  try {
    const response = await request<FetchSkuDetailsResponse>('/api/tools/sku-checker', {
      method: 'GET',
      params: params,
    });
    // Ensure response conforms to FetchSkuDetailsResponse, especially if API might not return all fields on error
    if (typeof response.success === 'undefined') {
      // If success field is missing, it might be an unexpected error format
      console.error('fetchSkuDetails: API response missing success field', response);
      return {
        data: [],
        current_batch_count: 0,
        grand_total: 0,
        success: false,
        error: 'Invalid response format from server.',
      };
    }
    return response;
  } catch (error: any) {
    console.error('fetchSkuDetails error:', error);
    let errorMessage = 'Failed to fetch SKU details from server.';
    if (error?.response?.data?.detail) {
      if (typeof error.response.data.detail === 'string') {
        errorMessage = error.response.data.detail;
      } else if (Array.isArray(error.response.data.detail) && error.response.data.detail.length > 0 && error.response.data.detail[0].msg) {
        // Handle FastAPI validation error array format
        errorMessage = error.response.data.detail.map((d: any) => `${d.loc.join('.')} - ${d.msg}`).join('; ');
      } else if (typeof error.response.data.detail === 'object') {
        errorMessage = JSON.stringify(error.response.data.detail);
      }
    } else if (error?.message) {
      errorMessage = error.message;
    }

    return {
      data: [],
      current_batch_count: 0,
      grand_total: 0,
      success: false,
      error: errorMessage,
    };
  }
}

// Assuming global API types are generated by UmiJS or defined elsewhere.
// For example:
// declare namespace API {
//   export type PriceCheckerSearchRequest = {
//     search_term: string;
//   };
//   export type PriceInfoItem = {
//     item_code?: string;
//     barcode?: string;
//     item_name?: string;
//     price?: number;
//     uom?: string;
//     last_updated?: string;
//   };
//   export type PriceCheckerResponse = {
//     data?: PriceInfoItem;
//     success: boolean;
//     message?: string;
//   };
// }

// --- Price Checker Tool Types ---
export interface PriceCheckerSearchRequestArgs {
  mcode: string;
  branch_code?: string; // Optional branch code
}

// New enhanced search request for both MCode and ArtNo
export interface PriceCheckerEnhancedSearchRequest {
  search_type: 'mcode' | 'artno';
  search_value: string;
  branch_code?: string;
}

export interface PriceInfoItemData {
  armscode?: string;
  mcode?: string;
  artno?: string;
  sku_description?: string;
  brand?: string;
  department?: string;
  category?: string;
  color?: string;
  size?: string; 
  rsp_price?: number;
  rsp_discount?: string;
  selling_price?: number;
  last_update?: string;
  // Conditional branch fields
  branch_code_result?: string;
  branch_name?: string;
  branch_price?: number;
  branch_price_update?: string;
}

export interface PriceCheckerApiResponse {
  data?: PriceInfoItemData;
  success: boolean;
  message?: string;
}

// New enhanced response for multiple results (ArtNo search)
export interface PriceCheckerEnhancedResponse {
  data?: PriceInfoItemData[];
  search_type: string;
  search_value: string;
  total_variants: number;
  success: boolean;
  message?: string;
}

// Branch price information interface
export interface BranchPriceInfo {
  branch_code: string;
  branch_name: string;
  branch_price?: number;
  branch_price_update?: string;
}

// Enhanced response with branch prices for MCode searches
export interface PriceCheckerWithBranchPricesResponse {
  data?: PriceInfoItemData[];
  branch_prices?: BranchPriceInfo[];
  search_type: string;
  search_value: string;
  total_variants: number;
  success: boolean;
  message?: string;
}

/**
 * Original price check API for MCode only (backward compatibility)
 */
export async function getPriceInfo(params: PriceCheckerSearchRequestArgs): Promise<PriceCheckerApiResponse> {
  try {
    const response = await request<PriceCheckerApiResponse>('/api/tools/price-check', {
      method: 'POST',
      data: params,
    });
    return response;
  } catch (error: any) {
    console.error('getPriceInfo error:', error);
    return {
      success: false,
      message: error?.response?.data?.detail || error?.message || 'Failed to fetch price information.',
    };
  }
}

/**
 * Enhanced price check API for both MCode and ArtNo search
 */
export async function getPriceInfoEnhanced(params: PriceCheckerEnhancedSearchRequest): Promise<PriceCheckerEnhancedResponse> {
  try {
    const response = await request<PriceCheckerEnhancedResponse>('/api/tools/price-check-enhanced', {
      method: 'POST',
      data: params,
    });
    return response;
  } catch (error: any) {
    console.error('getPriceInfoEnhanced error:', error);
    return {
      data: [],
      search_type: params.search_type,
      search_value: params.search_value,
      total_variants: 0,
      success: false,
      message: error?.response?.data?.detail || error?.message || 'Failed to fetch price information.',
    };
  }
}

/**
 * Enhanced price check API for MCode searches with all branch prices
 */
export async function getPriceInfoWithBranches(params: PriceCheckerEnhancedSearchRequest): Promise<PriceCheckerWithBranchPricesResponse> {
  try {
    const response = await request<PriceCheckerWithBranchPricesResponse>('/api/tools/price-check-with-branches', {
      method: 'POST',
      data: params,
    });
    return response;
  } catch (error: any) {
    console.error('getPriceInfoWithBranches error:', error);
    return {
      data: [],
      branch_prices: [],
      search_type: params.search_type,
      search_value: params.search_value,
      total_variants: 0,
      success: false,
      message: error?.response?.data?.detail || error?.message || 'Failed to fetch price information with branch prices.',
    };
  }
}

// --- SKU Checker Export Types and Function ---
export interface SkuCheckerExportRequest {
  sku_item_code?: string;
  artno?: string;
  mcode?: string;
  description?: string;
  search_scope?: string;
  use_wildcard?: boolean;
}

export interface SkuCheckerExportResponse {
  success: boolean;
  message: string;
  export_id?: number;
}

/**
 * Triggers SKU Checker export to Excel.
 * @param params The export parameters containing search criteria.
 * @returns A promise that resolves to the export response.
 */
export async function exportSkuChecker(params: SkuCheckerExportRequest): Promise<SkuCheckerExportResponse> {
  try {
    const response = await request<SkuCheckerExportResponse>('/api/tools/sku-checker/export', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: params,
    });
    return response;
  } catch (error: any) {
    console.error('exportSkuChecker error:', error);
    let errorMessage = 'Failed to trigger SKU Checker export.';
    if (error?.response?.data?.detail) {
      if (typeof error.response.data.detail === 'string') {
        errorMessage = error.response.data.detail;
      } else if (Array.isArray(error.response.data.detail) && error.response.data.detail.length > 0 && error.response.data.detail[0].msg) {
        errorMessage = error.response.data.detail.map((d: any) => `${d.loc.join('.')} - ${d.msg}`).join('; ');
      }
    } else if (error?.message) {
      errorMessage = error.message;
    }

    return {
      success: false,
      message: errorMessage,
    };
  }
}

// --- SKU Image Management Functions ---

/**
 * Gets all images for a specific article number.
 * @param artno The article number to get images for.
 * @returns A promise that resolves to the images list response.
 */
export async function getSkuImages(artno: string): Promise<SkuImagesListResponse> {
  try {
    const response = await request<SkuImagesListResponse>(`/api/tools/sku-images/${artno}`, {
      method: 'GET',
    });
    return response;
  } catch (error: any) {
    console.error('getSkuImages error:', error);
    let errorMessage = 'Failed to fetch SKU images.';
    if (error?.response?.data?.detail) {
      errorMessage = typeof error.response.data.detail === 'string' 
        ? error.response.data.detail 
        : JSON.stringify(error.response.data.detail);
    } else if (error?.message) {
      errorMessage = error.message;
    }

    return {
      success: false,
      images: [],
      total_count: 0,
      message: errorMessage,
    };
  }
}

/**
 * Searches for images using Google Custom Search API.
 * @param params The search parameters.
 * @returns A promise that resolves to the Google image search response.
 */
export async function searchGoogleImages(params: GoogleImageSearchRequest): Promise<GoogleImageSearchResponse> {
  try {
    const response = await request<GoogleImageSearchResponse>('/api/tools/sku-images/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: params,
    });
    return response;
  } catch (error: any) {
    console.error('searchGoogleImages error:', error);
    let errorMessage = 'Failed to search Google images.';
    if (error?.response?.data?.detail) {
      errorMessage = typeof error.response.data.detail === 'string' 
        ? error.response.data.detail 
        : JSON.stringify(error.response.data.detail);
    } else if (error?.message) {
      errorMessage = error.message;
    }

    return {
      success: false,
      results: [],
      total_results: 0,
      message: errorMessage,
    };
  }
}

/**
 * Saves an image from URL to local storage.
 * @param params The save request parameters.
 * @returns A promise that resolves to the save response.
 */
export async function saveImageFromUrl(params: SkuImageSaveRequest): Promise<SkuImageResponse> {
  try {
    const response = await request<SkuImageResponse>('/api/tools/sku-images/save-from-url', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: params,
    });
    return response;
  } catch (error: any) {
    console.error('saveImageFromUrl error:', error);
    let errorMessage = 'Failed to save image from URL.';
    if (error?.response?.data?.detail) {
      errorMessage = typeof error.response.data.detail === 'string' 
        ? error.response.data.detail 
        : JSON.stringify(error.response.data.detail);
    } else if (error?.message) {
      errorMessage = error.message;
    }

    return {
      success: false,
      message: errorMessage,
    };
  }
}

/**
 * Uploads an image from base64 data.
 * @param params The upload request parameters.
 * @returns A promise that resolves to the upload response.
 */
export async function uploadImage(params: SkuImageUploadRequest): Promise<SkuImageResponse> {
  try {
    const response = await request<SkuImageResponse>('/api/tools/sku-images/upload', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: params,
    });
    return response;
  } catch (error: any) {
    console.error('uploadImage error:', error);
    let errorMessage = 'Failed to upload image.';
    if (error?.response?.data?.detail) {
      errorMessage = typeof error.response.data.detail === 'string' 
        ? error.response.data.detail 
        : JSON.stringify(error.response.data.detail);
    } else if (error?.message) {
      errorMessage = error.message;
    }

    return {
      success: false,
      message: errorMessage,
    };
  }
}

/**
 * Uploads multiple images from base64 data with automatic optimization.
 * @param params The bulk upload request parameters.
 * @returns A promise that resolves to the bulk upload response.
 */
export async function bulkUploadImages(params: BulkImageUploadRequest): Promise<BulkImageUploadResponse> {
  try {
    const response = await request<BulkImageUploadResponse>('/api/tools/sku-images/bulk-upload', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: params,
    });
    return response;
  } catch (error: any) {
    console.error('bulkUploadImages error:', error);
    let errorMessage = 'Failed to upload images.';
    if (error?.response?.data?.detail) {
      errorMessage = typeof error.response.data.detail === 'string' 
        ? error.response.data.detail 
        : JSON.stringify(error.response.data.detail);
    } else if (error?.message) {
      errorMessage = error.message;
    }

    return {
      total_requested: 0,
      successful_uploads: 0,
      failed_uploads: 0,
      results: [],
      success: false,
      message: errorMessage,
    };
  }
}

/**
 * Deletes a specific SKU image.
 * @param imageId The image ID to delete.
 * @returns A promise that resolves to the delete response.
 */
export async function deleteSkuImage(imageId: number): Promise<SkuImageResponse> {
  try {
    const response = await request<SkuImageResponse>(`/api/tools/sku-images/${imageId}`, {
      method: 'DELETE',
    });
    return response;
  } catch (error: any) {
    console.error('deleteSkuImage error:', error);
    let errorMessage = 'Failed to delete image.';
    if (error?.response?.data?.detail) {
      errorMessage = typeof error.response.data.detail === 'string' 
        ? error.response.data.detail 
        : JSON.stringify(error.response.data.detail);
    } else if (error?.message) {
      errorMessage = error.message;
    }

    return {
      success: false,
      message: errorMessage,
    };
  }
}

// Existing SKU Checker interfaces
export interface SkuDetail {
  sku_item_code: string;
  description: string;
  brand: string;
  department: string;
  category: string;
  gender: string;
  mcode: string;
  cost_price: string | number;
  rsp_price: string | number;
  stock_quantity: number;
  last_updated: string;
}

export interface SkuCheckerResponse {
  found: boolean;
  details?: SkuDetail;
  message?: string;
}

export async function searchSku(skuItemCode: string): Promise<SkuCheckerResponse> {
  return request(`/api/tools/sku-checker`, {
    method: 'GET',
    params: { sku_item_code: skuItemCode },
  });
}

// Stock Take interfaces
export interface StockTakeSkuMasterItem {
  active_status?: string;
  sku_item_code: string;
  art_no?: string;
  mcode?: string;
  art_no_description?: string;
  brand?: string;
  department?: string;
  category?: string;
  type_of_silhouette?: string;
  silhouette?: string;
  color?: string;
  size?: string;
  vendor?: string;
  season_code?: string;
  rsp_price?: number;
  rsp_discount?: number;
  lastupdate?: string;
  is_parent?: string;
}

export interface StockTakeSkuMasterResponse {
  skus: StockTakeSkuMasterItem[];
  total_count: number;
  limit?: number;
  offset: number;
  has_more: boolean;
}

export interface StockTakeSessionCreate {
  branch_code: string;
  session_name: string;
  session_date: string;
  notes?: string;
}

export interface StockTakeSession {
  id: string;
  branch_code: string;
  user_id: number;
  session_name: string;
  session_date: string;
  status: string;
  notes?: string;
  created_at: string;
  synced_at?: string;
}

export interface StockTakeSessionsResponse {
  sessions: StockTakeSession[];
}

export interface StockTakeItem {
  sku_item_code: string;
  counted_quantity: number;
  system_quantity: number;
  location?: string;
  shelf?: string;
  notes?: string;
  counted_at?: string;
}

export interface StockTakeItemWithDetails {
  id: number;
  session_id: string;
  sku_item_code: string;
  counted_quantity: number;
  system_quantity: number;
  variance: number;
  notes?: string;
  counted_at: string;
  created_at: string;
}

export interface StockTakeItemsRequest {
  items: StockTakeItem[];
}

export interface StockTakeItemsResponse {
  success: boolean;
  saved_count: number;
  total_items: number;
}

export interface StockTakeItemsListResponse {
  items: StockTakeItemWithDetails[];
}

// Stock Take API functions

export async function downloadSkuMasterData(
  limit: number = 1000,
  offset: number = 0,
  search_term?: string
): Promise<StockTakeSkuMasterResponse> {
  return request('/api/tools/stock-take/sku-master', {
    method: 'GET',
    params: { limit, offset, search_term }
  });
}

export async function createStockTakeSession(
  sessionData: StockTakeSessionCreate
): Promise<StockTakeSession> {
  return request('/api/tools/stock-take/sessions', {
    method: 'POST',
    data: sessionData
  });
}

export async function getUserStockTakeSessions(): Promise<StockTakeSessionsResponse> {
  return request('/api/tools/stock-take/sessions', {
    method: 'GET'
  });
}

export async function getUserBranchesForStockTake(): Promise<{ branches: any[]; is_admin: boolean }> {
  return request('/api/tools/stock-take/user-branches', {
    method: 'GET',
  });
}

export async function saveStockTakeItems(
  sessionId: string,
  items: StockTakeItem[]
): Promise<StockTakeItemsResponse> {
  return request(`/api/tools/stock-take/sessions/${sessionId}/items`, {
    method: 'POST',
    data: { items }
  });
}

export async function getStockTakeItems(
  sessionId: string
): Promise<StockTakeItemsListResponse> {
  return request(`/api/tools/stock-take/sessions/${sessionId}/items`, {
    method: 'GET'
  });
}

export async function completeStockTakeSession(
  sessionId: string
): Promise<{ success: boolean; message: string }> {
  return request(`/api/tools/stock-take/sessions/${sessionId}/complete`, {
    method: 'POST'
  });
}

export interface StockTakeExportResponse {
  export_id: number;
  message: string;
  status: string;
}

export async function exportStockTakeSession(sessionId: string): Promise<StockTakeExportResponse> {
  return request(`/api/tools/stock-take/sessions/${sessionId}/export`, {
    method: 'POST'
  });
}