import smtplib
import logging
from email.mime.text import MIME<PERSON>ext
from email.mime.multipart import MIME<PERSON>ultip<PERSON>
from typing import List, Optional
from jinja2 import Template
from mailjet_rest import Client
import asyncio
from functools import partial

# Import the new email log service
from .email_log_service import email_log_service

try:
    from src.config import (
    MAILJET_API_KEY, 
    MAILJET_SECRET_KEY, 
    MA<PERSON><PERSON>ET_SMTP_HOST, 
    MAILJET_SMTP_PORT, 
    MAIL<PERSON>ET_USE_TLS,
    MAIL_FROM_EMAIL,
    MAIL_FROM_NAME,
    FRONTEND_URL
)
except ImportError:
    # For standalone testing
    from src.config import (
        MAILJET_API_KEY, 
        MA<PERSON><PERSON>ET_SECRET_KEY, 
        <PERSON><PERSON><PERSON><PERSON>_SMTP_HOST, 
        MAILJET_SMTP_PORT, 
        <PERSON><PERSON><PERSON>ET_USE_TLS,
        MAIL_FROM_EMAIL,
        MAIL_FROM_NAME,
        FRONTEND_URL
    )

logger = logging.getLogger(__name__)

class MailService:
    def __init__(self):
        self.api_key = MAILJET_API_KEY
        self.secret_key = MAILJET_SECRET_KEY
        self.smtp_host = MAILJET_SMTP_HOST
        self.smtp_port = MAILJET_SMTP_PORT
        self.use_tls = MAILJET_USE_TLS
        self.from_email = MAIL_FROM_EMAIL
        self.from_name = MAIL_FROM_NAME
        self.frontend_url = FRONTEND_URL
        
        # Initialize Mailjet API client
        if self.api_key and self.secret_key:
            self.mailjet = Client(auth=(self.api_key, self.secret_key), version='v3.1')
        else:
            self.mailjet = None
            logger.warning("Mailjet API credentials not configured. Email sending disabled.")

    async def send_email_api(self, to_email: str, subject: str, html_content: str, text_content: Optional[str] = None, cc_emails: Optional[List[str]] = None) -> bool:
        """Send email using Mailjet API"""
        if not self.mailjet:
            logger.error("Mailjet API client not configured")
            return False
            
        try:
            data = {
                'Messages': [
                    {
                        "From": {
                            "Email": self.from_email,
                            "Name": self.from_name
                        },
                        "To": [
                            {
                                "Email": to_email,
                                "Name": to_email.split("@")[0]
                            }
                        ],
                        "Cc": [{"Email": email, "Name": email.split("@")[0]} for email in cc_emails] if cc_emails else [],
                        "Subject": subject,
                        "TextPart": text_content or "Please enable HTML to view this email",
                        "HTMLPart": html_content,
                        "CustomID": f"portal-email-{to_email}"
                    }
                ]
            }
            
            # Run API call in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None, 
                partial(self.mailjet.send.create, data=data)
            )
            
            if result.status_code == 200:
                logger.info(f"Email sent successfully to {to_email}")
                await email_log_service.log_email(
                    recipient=to_email, subject=subject, status='sent', 
                    cc_recipients=cc_emails, body=html_content, source='mail_service_api'
                )
                return True
            else:
                error_message = f"Mailjet API Error {result.status_code}: {result.json()}"
                logger.error(f"Failed to send email to {to_email}: {error_message}")
                await email_log_service.log_email(
                    recipient=to_email, subject=subject, status='failed', 
                    cc_recipients=cc_emails, body=html_content, error_message=error_message, source='mail_service_api'
                )
                return False
                
        except Exception as e:
            error_message = f"Exception in send_email_api: {str(e)}"
            logger.error(f"Error sending email via API to {to_email}: {error_message}")
            await email_log_service.log_email(
                recipient=to_email, subject=subject, status='failed', 
                cc_recipients=cc_emails, body=html_content, error_message=error_message, source='mail_service_api'
            )
            return False

    async def send_email_smtp(self, to_email: str, subject: str, html_content: str, text_content: Optional[str] = None, cc_emails: Optional[List[str]] = None) -> bool:
        """Send email using SMTP"""
        try:
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = f"{self.from_name} <{self.from_email}>"
            msg['To'] = to_email
            if cc_emails:
                msg['Cc'] = ", ".join(cc_emails)

            # Add text and HTML parts
            if text_content:
                text_part = MIMEText(text_content, 'plain')
                msg.attach(text_part)
            
            html_part = MIMEText(html_content, 'html')
            msg.attach(html_part)

            # Run SMTP in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self._send_smtp, msg, [to_email] + (cc_emails or []))
            
            logger.info(f"Email sent successfully via SMTP to {to_email}")
            await email_log_service.log_email(
                recipient=to_email, subject=subject, status='sent', 
                cc_recipients=cc_emails, body=html_content, source='mail_service_smtp'
            )
            return True
            
        except Exception as e:
            error_message = f"Exception in send_email_smtp: {str(e)}"
            logger.error(f"Error sending email via SMTP to {to_email}: {error_message}")
            await email_log_service.log_email(
                recipient=to_email, subject=subject, status='failed', 
                cc_recipients=cc_emails, body=html_content, error_message=error_message, source='mail_service_smtp'
            )
            return False

    def _send_smtp(self, msg: MIMEMultipart, to_emails: List[str]):
        """Helper method for SMTP sending (runs in thread pool)"""
        # Use SMTP_SSL for port 465, regular SMTP for port 587
        if self.smtp_port == 465:
            # Use SMTP_SSL for port 465
            with smtplib.SMTP_SSL(self.smtp_host, self.smtp_port) as server:
                if self.api_key and self.secret_key:
                    server.login(self.api_key, self.secret_key)
                server.send_message(msg)
        else:
            # Use regular SMTP with STARTTLS for port 587
            with smtplib.SMTP(self.smtp_host, self.smtp_port) as server:
                if self.use_tls:
                    server.starttls()
                if self.api_key and self.secret_key:
                    server.login(self.api_key, self.secret_key)
                server.send_message(msg)

    async def send_email(self, to_email: str, subject: str, html_content: str, text_content: Optional[str] = None, method: str = "api", cc_emails: Optional[List[str]] = None) -> bool:
        """
        Send email using specified method (api or smtp)
        Fallback to SMTP if API fails
        """
        if method == "api":
            success = await self.send_email_api(to_email, subject, html_content, text_content, cc_emails=cc_emails)
            if not success:
                logger.info("API method failed, trying SMTP fallback")
                success = await self.send_email_smtp(to_email, subject, html_content, text_content, cc_emails=cc_emails)
            return success
        else:
            return await self.send_email_smtp(to_email, subject, html_content, text_content, cc_emails=cc_emails)

    def get_password_reset_template(self, username: str, reset_token: str) -> tuple[str, str]:
        """Generate password reset email template"""
        reset_url = f"{self.frontend_url}/user/reset-password?token={reset_token}"
        
        # HTML template
        html_template = Template("""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Password Reset Request</title>
            <style>
                .container { max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; }
                .header { background-color: #1890ff; color: white; padding: 20px; text-align: center; }
                .content { padding: 30px; background-color: #f5f5f5; }
                .button { 
                    display: inline-block; 
                    background-color: #1890ff; 
                    color: white; 
                    padding: 12px 24px; 
                    text-decoration: none; 
                    border-radius: 4px; 
                    margin: 20px 0; 
                }
                .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
                .warning { background-color: #fff2e8; border-left: 4px solid #fa8c16; padding: 15px; margin: 20px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Password Reset Request</h1>
                </div>
                <div class="content">
                    <h2>Hello {{ username }},</h2>
                    <p>We received a request to reset your password for your Portal account.</p>
                    <p>Click the button below to reset your password:</p>
                    <a href="{{ reset_url }}" class="button">Reset Password</a>
                    <div class="warning">
                        <strong>Important:</strong>
                        <ul>
                            <li>This link will expire in 30 minutes</li>
                            <li>If you didn't request this reset, please ignore this email</li>
                            <li>For security, this link can only be used once</li>
                        </ul>
                    </div>
                    <p>If the button doesn't work, copy and paste this link into your browser:</p>
                    <p style="word-break: break-all; color: #666;">{{ reset_url }}</p>
                </div>
                <div class="footer">
                    <p>This is an automated message from Portal System. Please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>
        """)
        
        # Text template
        text_template = Template("""
        Password Reset Request

        Hello {{ username }},

        We received a request to reset your password for your Portal account.

        Please visit the following link to reset your password:
        {{ reset_url }}

        Important:
        - This link will expire in 30 minutes
        - If you didn't request this reset, please ignore this email
        - For security, this link can only be used once

        This is an automated message from Portal System. Please do not reply to this email.
        """)
        
        html_content = html_template.render(username=username, reset_url=reset_url)
        text_content = text_template.render(username=username, reset_url=reset_url)
        
        return html_content, text_content

    def get_notification_template(self, title: str, message: str, action_url: Optional[str] = None) -> tuple[str, str]:
        """Generate notification email template"""
        
        # HTML template
        html_template = Template("""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{{ title }}</title>
            <style>
                .container { max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; }
                .header { background-color: #52c41a; color: white; padding: 20px; text-align: center; }
                .content { padding: 30px; background-color: #f5f5f5; }
                .button { 
                    display: inline-block; 
                    background-color: #52c41a; 
                    color: white; 
                    padding: 12px 24px; 
                    text-decoration: none; 
                    border-radius: 4px; 
                    margin: 20px 0; 
                }
                .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>{{ title }}</h1>
                </div>
                <div class="content">
                    <p>{{ message }}</p>
                    {% if action_url %}
                    <a href="{{ action_url }}" class="button">View Details</a>
                    {% endif %}
                </div>
                <div class="footer">
                    <p>This is an automated message from Portal System. Please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>
        """)
        
        # Text template
        text_template = Template("""
        {{ title }}

        {{ message }}

        {% if action_url %}
        View details: {{ action_url }}
        {% endif %}

        This is an automated message from Portal System. Please do not reply to this email.
        """)
        
        html_content = html_template.render(title=title, message=message, action_url=action_url)
        text_content = text_template.render(title=title, message=message, action_url=action_url)
        
        return html_content, text_content

    async def send_password_reset_email(self, to_email: str, username: str, reset_token: str) -> dict:
        """Send password reset email"""
        try:
            html_content, text_content = self.get_password_reset_template(username, reset_token)
            success = await self.send_email(
                to_email=to_email,
                subject="Password Reset Request - Portal System",
                html_content=html_content,
                text_content=text_content
            )
            
            if success:
                return {"success": True, "message": f"Password reset email sent successfully to {to_email}"}
            else:
                return {"success": False, "error": f"Failed to send password reset email to {to_email}"}
                
        except Exception as e:
            return {"success": False, "error": f"Password reset email sending failed: {str(e)}"}

    async def send_notification_email(self, to_email: str, title: str, message: str, action_url: Optional[str] = None) -> dict:
        """Send notification email"""
        try:
            html_content, text_content = self.get_notification_template(title, message, action_url)
            success = await self.send_email(
                to_email=to_email,
                subject=f"{title} - Portal System",
                html_content=html_content,
                text_content=text_content
            )
            
            if success:
                return {"success": True, "message": f"Email sent successfully to {to_email}"}
            else:
                return {"success": False, "error": f"Failed to send email to {to_email}"}
                
        except Exception as e:
            return {"success": False, "error": f"Email sending failed: {str(e)}"}

# Global instance
mail_service = MailService() 