"use strict";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
import React, { useState, useEffect, useRef } from "react";
import {
  PageContainer,
  ProCard,
  ProTable
} from "@ant-design/pro-components";
import {
  Button,
  Descriptions,
  Typography,
  Alert,
  Space,
  Spin,
  Tag,
  Empty,
  Input,
  Checkbox,
  Row,
  Col,
  message,
  ConfigProvider,
  Modal,
  Radio
} from "antd";
import { fetchSkuDetails, exportSku<PERSON>he<PERSON>, fetchSkuSalesSummary } from "@/services/tools";
import ExportHistory from "@/components/ExportHistory";
import SkuImageGallery from "@/components/SkuImageGallery";
import QOHModal from "@/components/QOHModal";
import { DownloadOutlined, CameraOutlined, PictureOutlined, SearchOutlined, QrcodeOutlined, ShopOutlined, BarChartOutlined } from "@ant-design/icons";
import enUS from "antd/locale/en_US";
import { useModel } from "@umijs/max";
import Quagga from "@ericblade/quagga2";
import { useHasCostPermission, useHasManageSkuImagePermission } from "@/utils/permissions";
import JsBarcode from "jsbarcode";
import jsPDF from "jspdf";
const { Text } = Typography;
const getHighlightedText = (textInput, highlight = "") => {
  const text = String(textInput || "");
  if (!highlight.trim()) {
    return /* @__PURE__ */ jsx("span", { children: text });
  }
  const highlightStr = String(highlight || "");
  const parts = text.split(new RegExp(`(${highlightStr.replace(/[.*+?^${}\\'()'|[\]\\]/g, "\\\\$&")})`, "gi"));
  return /* @__PURE__ */ jsx("span", { children: parts.map(
    (part, i) => part.toLowerCase() === highlight.toLowerCase() ? /* @__PURE__ */ jsx("span", { style: { backgroundColor: "#ffc069", padding: 0 }, children: part }, i) : part
  ) });
};
const SkuCheckerPage = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchResults, setSearchResults] = useState([]);
  const [selectedSku, setSelectedSku] = useState(null);
  const [apiTotalResults, setApiTotalResults] = useState(0);
  const [searchType, setSearchType] = useState("artno");
  const [searchValue, setSearchValue] = useState("");
  const [searchScope, setSearchScope] = useState("parent");
  const [useWildcard, setUseWildcard] = useState(false);
  const [searchScopeDisabled, setSearchScopeDisabled] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [currentPageSize, setCurrentPageSize] = useState(10);
  const [searchPerformed, setSearchPerformed] = useState(false);
  const [currentMainSearchValue, setCurrentMainSearchValue] = useState("");
  const [currentMainSearchType, setCurrentMainSearchType] = useState("artno");
  const [tableSearchText, setTableSearchText] = useState("");
  const [isScannerModalVisible, setIsScannerModalVisible] = useState(false);
  const [scannerInitializing, setScannerInitializing] = useState(false);
  const [scannerError, setScannerError] = useState(null);
  const [triggerSearch, setTriggerSearch] = useState(false);
  const interactiveRef = useRef(null);
  const quaggaInitializedRef = useRef(false);
  const [imageGalleryVisible, setImageGalleryVisible] = useState(false);
  const [currentArtno, setCurrentArtno] = useState("");
  const [isLargeResultModalVisible, setIsLargeResultModalVisible] = useState(false);
  const [largeResultMessage, setLargeResultMessage] = useState("");
  const [currentSearchParams, setCurrentSearchParams] = useState(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [barcodeModalVisible, setBarcodeModalVisible] = useState(false);
  const [generatingPDF, setGeneratingPDF] = useState(false);
  const barcodePreviewRef = useRef(null);
  const [qohModalVisible, setQohModalVisible] = useState(false);
  const [qohSelectedSku, setQohSelectedSku] = useState(null);
  const [salesModalVisible, setSalesModalVisible] = useState(false);
  const [salesLoading, setSalesLoading] = useState(false);
  const [salesData, setSalesData] = useState(null);
  const [salesSelectedSku, setSalesSelectedSku] = useState(null);
  const [salesSearchText, setSalesSearchText] = useState("");
  const searchTypeLabels = {
    sku_item_code: "ARMSCode",
    artno: "Art No",
    mcode: "MCode",
    description: "Description"
  };
  const isCodeSearchType = () => {
    return ["sku_item_code", "artno", "mcode"].includes(searchType);
  };
  const getSearchPlaceholder = () => {
    const basePlaceholder = `Enter ${searchTypeLabels[searchType]}`;
    if (isCodeSearchType()) {
      return `${basePlaceholder}(s), one per line`;
    }
    return basePlaceholder;
  };
  const handleSearch = async () => {
    if (!searchValue.trim()) {
      setError("Please enter a search value.");
      setSearchResults([]);
      setSelectedSku(null);
      setSearchPerformed(true);
      setCurrentMainSearchValue("");
      return;
    }
    setLoading(true);
    setError(null);
    setSearchPerformed(true);
    setCurrentMainSearchValue(searchValue);
    setCurrentMainSearchType(searchType);
    const params = {
      page_size: 1e4,
      // Large number to get all results
      page: 1,
      search_scope: searchScope
    };
    if (isCodeSearchType()) {
      params[searchType] = searchValue;
      params.use_wildcard = useWildcard;
    } else {
      params.description = searchValue;
    }
    try {
      const response = await fetchSkuDetails(params);
      if (response.success && response.data) {
        setSearchResults(response.data);
        setApiTotalResults(response.grand_total);
        setCurrentPage(1);
        if (response.is_result_limited && response.export_recommended) {
          setLargeResultMessage(response.result_limit_message || "Large result set detected");
          setCurrentSearchParams(params);
          setIsLargeResultModalVisible(true);
        }
        if (response.data.length > 0) {
          console.log("First item in searchResults:", JSON.stringify(response.data[0], null, 2));
        }
        if (response.data.length > 0) {
          setSelectedSku(response.data[0]);
          if (response.data.length === 1 && error && error.startsWith("Multiple items found")) {
            setError(null);
          }
        } else {
          setError("No SKU found matching your criteria.");
        }
      } else {
        setError(response.error || "Failed to fetch SKU details.");
        setApiTotalResults(0);
      }
    } catch (e) {
      console.error("Search error:", e);
      setError(e.message || "An unexpected error occurred.");
      setApiTotalResults(0);
    }
    setLoading(false);
  };
  const handleReset = () => {
    setSearchValue("");
    setSearchResults([]);
    setSelectedSku(null);
    setApiTotalResults(0);
    setCurrentPage(1);
    setCurrentPageSize(10);
    setError(null);
    setSearchPerformed(false);
    setCurrentMainSearchValue("");
    setTableSearchText("");
    setIsLargeResultModalVisible(false);
    setLargeResultMessage("");
    setCurrentSearchParams(null);
    setSelectedRowKeys([]);
    setSelectedRows([]);
    setBarcodeModalVisible(false);
  };
  useEffect(() => {
    if (searchType === "sku_item_code" || searchType === "mcode") {
      if (searchScope !== "all") {
        setSearchScope("all");
      }
      setSearchScopeDisabled(true);
    } else {
      setSearchScopeDisabled(false);
    }
  }, [searchType, searchScope]);
  const columns = [
    {
      title: "Actions",
      key: "actions",
      width: 140,
      fixed: "left",
      render: (text_ignored, record) => /* @__PURE__ */ jsxs(Space, { size: "small", children: [
        /* @__PURE__ */ jsx(
          Button,
          {
            type: "link",
            size: "small",
            icon: /* @__PURE__ */ jsx(ShopOutlined, {}),
            onClick: (e) => {
              e.stopPropagation();
              handleOpenQOHModal(record);
            },
            title: "Check QOH (Quantity On Hand)",
            style: { padding: "0 4px" },
            children: "QOH"
          }
        ),
        /* @__PURE__ */ jsx(
          Button,
          {
            type: "link",
            size: "small",
            icon: /* @__PURE__ */ jsx(BarChartOutlined, {}),
            onClick: (e) => {
              e.stopPropagation();
              handleOpenSalesModal(record);
            },
            title: "View Sales Summary",
            style: { padding: "0 4px" },
            children: "Sales"
          }
        )
      ] })
    },
    {
      title: "ARMS Code",
      dataIndex: "sku_item_code",
      key: "sku_item_code",
      width: 120,
      ellipsis: true,
      responsive: ["lg"],
      sorter: (a, b) => String(a.sku_item_code || "").localeCompare(String(b.sku_item_code || "")),
      render: (text_ignored, record, index) => {
        const cellValue = record.sku_item_code;
        if (index === 0) {
          console.log(`ARMSCode render - cellValue from record:`, cellValue, `original text arg:`, text_ignored);
        }
        const displayValue = String(cellValue === null || cellValue === void 0 ? "" : cellValue);
        return currentMainSearchType === "sku_item_code" ? getHighlightedText(displayValue, currentMainSearchValue) : displayValue;
      }
    },
    {
      title: "Art No",
      dataIndex: "Art_No",
      key: "Art_No",
      width: 120,
      ellipsis: true,
      sorter: (a, b) => String(a.Art_No || "").localeCompare(String(b.Art_No || "")),
      defaultSortOrder: "ascend",
      render: (text_ignored, record, index) => {
        const cellValue = record.Art_No;
        if (index === 0) {
          console.log(`Art_No render - cellValue from record:`, cellValue, `original text arg:`, text_ignored);
        }
        const displayValue = String(cellValue === null || cellValue === void 0 ? "" : cellValue);
        return currentMainSearchType === "artno" ? getHighlightedText(displayValue, currentMainSearchValue) : displayValue;
      }
    },
    {
      title: "MCode",
      dataIndex: "MCode",
      key: "MCode",
      width: 100,
      ellipsis: true,
      responsive: ["md"],
      sorter: (a, b) => String(a.MCode || "").localeCompare(String(b.MCode || "")),
      render: (text_ignored, record) => {
        const cellValue = record.MCode;
        const displayValue = String(cellValue === null || cellValue === void 0 ? "" : cellValue);
        return currentMainSearchType === "mcode" ? getHighlightedText(displayValue, currentMainSearchValue) : displayValue;
      }
    },
    {
      title: "Description",
      dataIndex: "ART_NO DESCRIPTION",
      key: "ART_NO DESCRIPTION",
      ellipsis: true,
      responsive: ["sm"],
      sorter: (a, b) => String(a["ART_NO DESCRIPTION"] || "").localeCompare(String(b["ART_NO DESCRIPTION"] || "")),
      render: (text_ignored, record) => {
        const cellValue = record["ART_NO DESCRIPTION"];
        const displayValue = String(cellValue === null || cellValue === void 0 ? "" : cellValue);
        return currentMainSearchType === "description" ? getHighlightedText(displayValue, currentMainSearchValue) : displayValue;
      }
    },
    {
      title: "Brand",
      dataIndex: "Brand",
      key: "Brand",
      width: 100,
      ellipsis: true,
      responsive: ["md"],
      sorter: (a, b) => String(a.Brand || "").localeCompare(String(b.Brand || "")),
      render: (text_ignored, record) => String(record.Brand === null || record.Brand === void 0 ? "N/A" : record.Brand)
    },
    {
      title: "Color",
      dataIndex: "Color",
      key: "Color",
      width: 80,
      ellipsis: true,
      responsive: ["lg"],
      sorter: (a, b) => String(a.Color || "").localeCompare(String(b.Color || "")),
      render: (text_ignored, record) => String(record.Color === null || record.Color === void 0 ? "N/A" : record.Color)
    },
    {
      title: "Size",
      dataIndex: "Size",
      key: "Size",
      width: 70,
      ellipsis: true,
      sorter: (a, b) => String(a.Size || "").localeCompare(String(b.Size || "")),
      render: (text_ignored, record) => String(record.Size === null || record.Size === void 0 ? "N/A" : record.Size)
    },
    {
      title: "RSP",
      dataIndex: "RSP",
      key: "RSP",
      width: 90,
      ellipsis: true,
      sorter: (a, b) => (a.RSP || 0) - (b.RSP || 0),
      render: (text_ignored, record) => {
        const rsp = record.RSP;
        return rsp ? `RM ${rsp.toFixed(2)}` : "N/A";
      }
    },
    {
      title: "RSP Disc",
      dataIndex: "RSP_Discount",
      key: "RSP_Discount",
      width: 90,
      ellipsis: true,
      responsive: ["md"],
      sorter: (a, b) => String(a.RSP_Discount || "").localeCompare(String(b.RSP_Discount || "")),
      render: (text_ignored, record) => {
        const discount = record.RSP_Discount;
        return /* @__PURE__ */ jsx(Tag, { color: "orange", style: { fontWeight: "bold", fontSize: "11px" }, children: discount || "N/A" });
      }
    },
    {
      title: "Net Price",
      dataIndex: "Net_Selling_Price",
      key: "Net_Selling_Price",
      width: 100,
      ellipsis: true,
      sorter: (a, b) => (a.Net_Selling_Price || 0) - (b.Net_Selling_Price || 0),
      render: (text_ignored, record) => {
        const netPrice = record.Net_Selling_Price;
        return /* @__PURE__ */ jsx(Tag, { color: "green", style: { fontWeight: "bold", fontSize: "11px" }, children: netPrice ? `RM ${netPrice.toFixed(2)}` : "N/A" });
      }
    },
    {
      title: "Season",
      dataIndex: "Season_Code",
      key: "Season_Code",
      width: 80,
      ellipsis: true,
      responsive: ["lg"],
      sorter: (a, b) => String(a.Season_Code || "").localeCompare(String(b.Season_Code || "")),
      render: (text_ignored, record) => String(record.Season_Code === null || record.Season_Code === void 0 ? "N/A" : record.Season_Code)
    },
    {
      title: "Status",
      dataIndex: "Active",
      key: "Active",
      width: 80,
      responsive: ["sm"],
      sorter: (a, b) => String(a.Active || "").localeCompare(String(b.Active || "")),
      render: (text_ignored, record) => {
        const activeStr = String(record.Active === null || record.Active === void 0 ? "Error" : record.Active);
        return /* @__PURE__ */ jsx(Tag, { color: activeStr === "Active" ? "green" : activeStr === "Inactive" ? "red" : "orange", style: { fontSize: "11px" }, children: activeStr });
      }
    },
    {
      title: "Parent",
      dataIndex: "is_parent",
      key: "is_parent",
      width: 70,
      responsive: ["lg"],
      sorter: (a, b) => (a.is_parent || "").localeCompare(b.is_parent || ""),
      render: (text_ignored, record) => {
        const isParent = record.is_parent;
        return isParent === "1" ? /* @__PURE__ */ jsx(Tag, { color: "blue", style: { fontSize: "11px" }, children: "Yes" }) : /* @__PURE__ */ jsx(Tag, { style: { fontSize: "11px" }, children: "No" });
      }
    }
  ];
  const filteredDisplayData = React.useMemo(() => {
    if (!tableSearchText) {
      return searchResults;
    }
    const lowerSearchText = tableSearchText.toLowerCase();
    return searchResults.filter((item) => {
      return Object.values(item).some(
        (value) => String(value || "").toLowerCase().includes(lowerSearchText)
      );
    });
  }, [searchResults, tableSearchText]);
  const renderImageSection = (sku) => {
    if (!sku) return null;
    return /* @__PURE__ */ jsx(
      ProCard,
      {
        title: /* @__PURE__ */ jsxs(Space, { children: [
          /* @__PURE__ */ jsx(Text, { strong: true, children: "Product Images" }),
          sku.image_count !== void 0 && /* @__PURE__ */ jsxs(Tag, { color: "blue", children: [
            sku.image_count,
            " image",
            sku.image_count === 1 ? "" : "s"
          ] })
        ] }),
        bordered: true,
        headerBordered: true,
        style: { marginTop: 16 },
        extra: hasManageImagePermission ? /* @__PURE__ */ jsx(
          Button,
          {
            type: "primary",
            icon: /* @__PURE__ */ jsx(PictureOutlined, {}),
            onClick: () => handleOpenImageGallery(sku.Art_No || ""),
            disabled: !sku.Art_No,
            children: "Manage Images"
          }
        ) : /* @__PURE__ */ jsx(
          Button,
          {
            icon: /* @__PURE__ */ jsx(PictureOutlined, {}),
            onClick: () => handleOpenImageGallery(sku.Art_No || ""),
            disabled: !sku.Art_No,
            children: "View Images"
          }
        ),
        children: sku.images && sku.images.length > 0 ? /* @__PURE__ */ jsxs(Row, { gutter: [16, 16], children: [
          sku.images.slice(0, 4).map((image, index) => /* @__PURE__ */ jsx(Col, { xs: 12, sm: 8, md: 6, children: /* @__PURE__ */ jsxs(
            "div",
            {
              style: {
                border: "1px solid #d9d9d9",
                borderRadius: "6px",
                overflow: "hidden",
                cursor: "pointer",
                transition: "all 0.3s ease"
              },
              onClick: () => handleOpenImageGallery(sku.Art_No || ""),
              children: [
                /* @__PURE__ */ jsx(
                  "img",
                  {
                    src: image.image_url,
                    alt: image.filename,
                    style: {
                      width: "100%",
                      height: "120px",
                      objectFit: "contain",
                      backgroundColor: "#fafafa"
                    },
                    onError: (e) => {
                      e.target.src = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2Y1ZjVmNSIvPgogIDx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiM5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIwLjNlbSI+Tm8gSW1hZ2U8L3RleHQ+Cjwvc3ZnPg==";
                    }
                  }
                ),
                /* @__PURE__ */ jsx("div", { style: { padding: "8px", background: "#fafafa" }, children: /* @__PURE__ */ jsx(Text, { style: { fontSize: "12px" }, ellipsis: true, children: image.filename }) })
              ]
            }
          ) }, image.id)),
          sku.images.length > 4 && /* @__PURE__ */ jsx(Col, { xs: 12, sm: 8, md: 6, children: /* @__PURE__ */ jsx(
            "div",
            {
              style: {
                border: "1px dashed #d9d9d9",
                borderRadius: "6px",
                height: "120px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                cursor: "pointer",
                background: "#fafafa"
              },
              onClick: () => handleOpenImageGallery(sku.Art_No || ""),
              children: /* @__PURE__ */ jsxs(Space, { direction: "vertical", style: { textAlign: "center" }, children: [
                /* @__PURE__ */ jsx(PictureOutlined, { style: { fontSize: "24px", color: "#1890ff" } }),
                /* @__PURE__ */ jsxs(Text, { style: { fontSize: "12px", color: "#666" }, children: [
                  "+",
                  sku.images.length - 4,
                  " more"
                ] })
              ] })
            }
          ) })
        ] }) : /* @__PURE__ */ jsx(
          Empty,
          {
            description: "No images available",
            image: Empty.PRESENTED_IMAGE_SIMPLE,
            children: hasManageImagePermission ? /* @__PURE__ */ jsx(
              Button,
              {
                type: "primary",
                icon: /* @__PURE__ */ jsx(PictureOutlined, {}),
                onClick: () => handleOpenImageGallery(sku.Art_No || ""),
                disabled: !sku.Art_No,
                children: "Add Images"
              }
            ) : /* @__PURE__ */ jsx(Text, { type: "secondary", children: "Contact your administrator to request image management permission" })
          }
        )
      }
    );
  };
  const renderSkuDetailsSection = (sku) => {
    if (!sku) {
      if (searchPerformed && searchResults.length > 0) {
        return /* @__PURE__ */ jsx(ProCard, { bordered: true, style: { marginTop: 16 }, children: /* @__PURE__ */ jsx(Empty, { description: "Select an item from the results table to see details." }) });
      }
      return null;
    }
    return /* @__PURE__ */ jsxs("div", { style: { marginTop: 16 }, children: [
      /* @__PURE__ */ jsx(ProCard, { title: /* @__PURE__ */ jsx(Text, { strong: true, children: "Basic Information" }), bordered: true, headerBordered: true, style: { marginBottom: 16 }, children: /* @__PURE__ */ jsxs(Descriptions, { bordered: true, column: { xs: 1, sm: 1, md: 2 }, layout: "horizontal", size: "small", children: [
        /* @__PURE__ */ jsx(Descriptions.Item, { label: "Status", span: 1, children: /* @__PURE__ */ jsx(Tag, { color: sku.Active === "Active" ? "green" : sku.Active === "Inactive" ? "red" : "orange", children: sku.Active }) }),
        /* @__PURE__ */ jsx(Descriptions.Item, { label: "ARMSCode", span: 1, children: sku.sku_item_code }),
        /* @__PURE__ */ jsx(Descriptions.Item, { label: "Art No", children: sku.Art_No || "N/A" }),
        /* @__PURE__ */ jsx(Descriptions.Item, { label: "MCode", children: sku.MCode || "N/A" }),
        /* @__PURE__ */ jsx(Descriptions.Item, { label: "Description", span: 2, children: sku["ART_NO DESCRIPTION"] || "N/A" }),
        /* @__PURE__ */ jsx(Descriptions.Item, { label: "Brand", children: sku.Brand || "N/A" }),
        /* @__PURE__ */ jsx(Descriptions.Item, { label: "Department", children: sku.Department || "N/A" }),
        /* @__PURE__ */ jsx(Descriptions.Item, { label: "Color", children: sku.Color || "N/A" }),
        /* @__PURE__ */ jsx(Descriptions.Item, { label: "Size", children: sku.Size || "N/A" })
      ] }) }),
      /* @__PURE__ */ jsx(ProCard, { title: /* @__PURE__ */ jsx(Text, { strong: true, children: "Pricing Information" }), bordered: true, headerBordered: true, style: { marginBottom: 16 }, children: /* @__PURE__ */ jsxs(Descriptions, { bordered: true, column: { xs: 1, sm: 1, md: 2 }, layout: "horizontal", size: "small", children: [
        /* @__PURE__ */ jsx(Descriptions.Item, { label: "RSP", children: sku.RSP ? `RM ${sku.RSP.toFixed(2)}` : "N/A" }),
        /* @__PURE__ */ jsx(Descriptions.Item, { label: "RSP Discount", children: /* @__PURE__ */ jsx(Tag, { color: "orange", style: { fontWeight: "bold" }, children: sku.RSP_Discount || "N/A" }) }),
        /* @__PURE__ */ jsx(Descriptions.Item, { label: "Net Selling Price", span: 2, children: /* @__PURE__ */ jsx(Tag, { color: "green", style: { fontWeight: "bold", fontSize: "14px" }, children: sku.Net_Selling_Price ? `RM ${sku.Net_Selling_Price.toFixed(2)}` : "N/A" }) })
      ] }) }),
      /* @__PURE__ */ jsx(ProCard, { title: /* @__PURE__ */ jsx(Text, { strong: true, children: "Extra Information" }), bordered: true, headerBordered: true, style: { marginBottom: 16 }, children: /* @__PURE__ */ jsxs(Descriptions, { bordered: true, column: { xs: 1, sm: 1, md: 2 }, layout: "horizontal", size: "small", children: [
        /* @__PURE__ */ jsx(Descriptions.Item, { label: "Cost Price", children: hasCostPermission ? sku.Cost_Price ? `RM ${sku.Cost_Price.toFixed(2)}` : "N/A" : "[No View Permission]" }),
        /* @__PURE__ */ jsx(Descriptions.Item, { label: "HQ Cost", children: hasCostPermission ? sku.HQ_Cost ? `RM ${sku.HQ_Cost.toFixed(2)}` : "N/A" : "[No View Permission]" }),
        /* @__PURE__ */ jsx(Descriptions.Item, { label: "Vendor", children: sku.Vendor || "N/A" }),
        /* @__PURE__ */ jsx(Descriptions.Item, { label: "Season Code", children: sku.Season_Code || "N/A" }),
        /* @__PURE__ */ jsx(Descriptions.Item, { label: "Category", children: sku.Category || "N/A" }),
        /* @__PURE__ */ jsx(Descriptions.Item, { label: "Silhouette", children: sku.Silhouette || "N/A" }),
        /* @__PURE__ */ jsx(Descriptions.Item, { label: "Type of Silhouette", span: 2, children: sku.Type_of_Silhouette || "N/A" }),
        /* @__PURE__ */ jsx(Descriptions.Item, { label: "Gender", children: sku.Gender || "N/A" }),
        /* @__PURE__ */ jsx(Descriptions.Item, { label: "Age", children: sku.Age || "N/A" }),
        /* @__PURE__ */ jsx(Descriptions.Item, { label: "Is Parent", children: sku.is_parent === "1" ? "Yes" : sku.is_parent === "0" ? "No" : "N/A" }),
        /* @__PURE__ */ jsx(Descriptions.Item, { label: "Last Update", children: sku.lastupdate ? new Date(sku.lastupdate).toLocaleString() : "N/A" })
      ] }) })
    ] });
  };
  const handleExport = async () => {
    if (!searchValue.trim()) {
      message.warning("Please provide search criteria before exporting.");
      return;
    }
    if (!searchPerformed || searchResults.length === 0) {
      message.warning("Please perform a search first to ensure there are results to export.");
      return;
    }
    const exportParams = {
      sku_item_code: searchType === "sku_item_code" ? searchValue : void 0,
      artno: searchType === "artno" ? searchValue : void 0,
      mcode: searchType === "mcode" ? searchValue : void 0,
      description: searchType === "description" ? searchValue : void 0,
      search_scope: searchScope,
      use_wildcard: useWildcard
    };
    message.loading({ content: "Starting export request...", key: "export" });
    try {
      const response = await exportSkuChecker(exportParams);
      if (response.success) {
        message.success({
          content: response.message || "Export started successfully! Check history below.",
          key: "export",
          duration: 5
        });
      } else {
        message.error({
          content: response.message || "Failed to start export.",
          key: "export",
          duration: 5
        });
      }
    } catch (e) {
      console.error("Export error:", e);
      message.error({
        content: e.message || "An unexpected error occurred during export.",
        key: "export",
        duration: 5
      });
    }
  };
  const handleExportFromModal = async () => {
    if (!currentSearchParams) {
      message.error("Search parameters not available for export.");
      return;
    }
    const exportParams = {
      sku_item_code: currentSearchParams.sku_item_code,
      artno: currentSearchParams.artno,
      mcode: currentSearchParams.mcode,
      description: currentSearchParams.description,
      search_scope: currentSearchParams.search_scope,
      use_wildcard: currentSearchParams.use_wildcard
    };
    message.loading({ content: "Starting export request...", key: "export" });
    try {
      const response = await exportSkuChecker(exportParams);
      if (response.success) {
        message.success({
          content: response.message || "Export started successfully! Check history below.",
          key: "export",
          duration: 5
        });
        setIsLargeResultModalVisible(false);
      } else {
        message.error({
          content: response.message || "Failed to start export.",
          key: "export",
          duration: 5
        });
      }
    } catch (e) {
      console.error("Export error:", e);
      message.error({
        content: e.message || "An unexpected error occurred during export.",
        key: "export",
        duration: 5
      });
    }
  };
  const { initialState } = useModel("@@initialState");
  const hasCostPermission = useHasCostPermission();
  const hasManageImagePermission = useHasManageSkuImagePermission();
  const initializeScanner = async () => {
    if (!isScannerModalVisible || !interactiveRef.current || quaggaInitializedRef.current) {
      return;
    }
    setScannerInitializing(true);
    setScannerError(null);
    try {
      if (typeof Quagga === "undefined") {
        setScannerError("Barcode scanner library (QuaggaJS) is not available. Please ensure it's installed and imported correctly.");
        setScannerInitializing(false);
        return;
      }
      Quagga.init({
        inputStream: {
          name: "Live",
          type: "LiveStream",
          target: interactiveRef.current,
          constraints: { width: { min: 640 }, height: { min: 480 }, facingMode: { ideal: "environment" } }
        },
        locator: { patchSize: "medium", halfSample: false },
        numOfWorkers: navigator.hardwareConcurrency || 2,
        decoder: { readers: ["code_128_reader", "ean_reader", "ean_8_reader", "code_39_reader", "upc_reader", "upc_e_reader"] },
        locate: true,
        frequency: 5
      }, (err) => {
        setScannerInitializing(false);
        if (err) {
          console.error("Quagga Init Error:", err);
          let errorMsg = "Camera initialization failed.";
          if (err.name === "NotAllowedError") errorMsg = "Camera access denied. Please enable camera permissions.";
          else if (err.name === "NotFoundError" || err.name === "DevicesNotFoundError") errorMsg = "No suitable camera found.";
          else if (err.name === "NotReadableError" || err.name === "TrackStartError") errorMsg = "Camera is already in use or cannot be accessed.";
          setScannerError(errorMsg);
          quaggaInitializedRef.current = false;
          return;
        }
        console.log("Quagga initialization finished. Starting scanner...");
        Quagga.start();
        quaggaInitializedRef.current = true;
      });
      Quagga.onDetected((result) => {
        const code = result.codeResult.code;
        if (code) {
          console.log("Barcode Detected:", code);
          setSearchValue(code);
          setSearchType("mcode");
          setIsScannerModalVisible(false);
          setTriggerSearch(true);
        }
      });
      Quagga.onProcessed((result) => {
        if (!quaggaInitializedRef.current || !interactiveRef.current) return;
        try {
          const drawingCtx = Quagga.canvas.ctx.overlay;
          const drawingCanvas = Quagga.canvas.dom.overlay;
          if (result && drawingCanvas instanceof HTMLCanvasElement && drawingCtx) {
            drawingCtx.clearRect(0, 0, drawingCanvas.width, drawingCanvas.height);
            if (result.boxes) {
              result.boxes.filter((box) => box !== result.box).forEach((box) => {
                Quagga.ImageDebug.drawPath(box, { x: 0, y: 1 }, drawingCtx, {
                  color: "green",
                  lineWidth: 2
                });
              });
            }
            if (result.box) {
              Quagga.ImageDebug.drawPath(result.box, { x: 0, y: 1 }, drawingCtx, {
                color: "blue",
                lineWidth: 2
              });
            }
          }
        } catch (e) {
        }
      });
    } catch (error2) {
      console.error("Error initializing scanner:", error2);
      setScannerError(error2.message || "Failed to initialize barcode scanner.");
      setScannerInitializing(false);
    }
  };
  const stopScanner = () => {
    if (quaggaInitializedRef.current && typeof Quagga !== "undefined") {
      try {
        Quagga.stop();
        console.log("Quagga scanner stopped.");
      } catch (e) {
        console.error("Error stopping Quagga:", e);
      }
      if (interactiveRef.current) {
        const canvasElement = interactiveRef.current.querySelector("canvas.drawingBuffer");
        if (canvasElement instanceof HTMLCanvasElement) {
          const context = canvasElement.getContext("2d");
          if (context) {
            context.clearRect(0, 0, canvasElement.width, canvasElement.height);
          }
        }
        const video = interactiveRef.current.querySelector("video");
        if (video) video.remove();
      }
      quaggaInitializedRef.current = false;
    }
  };
  const handleOpenScanner = () => {
    setScannerError(null);
    setIsScannerModalVisible(true);
  };
  const handleModalClose = () => {
    setIsScannerModalVisible(false);
  };
  const handleOpenImageGallery = (artno) => {
    setCurrentArtno(artno);
    setImageGalleryVisible(true);
  };
  const handleCloseImageGallery = () => {
    setImageGalleryVisible(false);
    setCurrentArtno("");
  };
  const handleImagesUpdated = async () => {
    if (selectedSku) {
      try {
        const params = {
          page_size: 1,
          page: 1,
          search_scope: searchScope
        };
        if (selectedSku.Art_No) {
          params.artno = selectedSku.Art_No;
        } else if (selectedSku.sku_item_code) {
          params.sku_item_code = selectedSku.sku_item_code;
        }
        const response = await fetchSkuDetails(params);
        if (response.success && response.data && response.data.length > 0) {
          const updatedSku = response.data[0];
          setSelectedSku(updatedSku);
          setSearchResults(
            (prevResults) => prevResults.map(
              (item) => item.sku_item_code === updatedSku.sku_item_code ? updatedSku : item
            )
          );
        }
      } catch (error2) {
        console.error("Error refreshing SKU data:", error2);
      }
    }
  };
  const handleOpenBarcodeGenerator = () => {
    if (selectedRows.length === 0) {
      message.warning("Please select at least one SKU to generate barcodes");
      return;
    }
    setBarcodeModalVisible(true);
  };
  const handleCloseBarcodeModal = () => {
    setBarcodeModalVisible(false);
  };
  const handleOpenQOHModal = (sku) => {
    setQohSelectedSku(sku);
    setQohModalVisible(true);
  };
  const handleCloseQOHModal = () => {
    setQohModalVisible(false);
    setQohSelectedSku(null);
  };
  const handleOpenSalesModal = async (sku) => {
    const artno = sku.Art_No || sku.artno || sku.art_no;
    const mcode = sku.MCode || sku.mcode || sku.Mcode;
    console.log("Sales modal SKU data:", {
      Art_No: sku.Art_No,
      MCode: sku.MCode,
      extracted_artno: artno,
      extracted_mcode: mcode,
      searchType: mcode ? "child_code" : "parent_code",
      fullSku: sku
    });
    if (!artno) {
      message.error(`Cannot fetch sales data: Art No is missing. Available fields: ${Object.keys(sku).join(", ")}`);
      return;
    }
    if (!mcode) {
      console.log("No MCode found - treating as parent code search for all child variants");
    }
    setSalesSelectedSku(sku);
    setSalesModalVisible(true);
    setSalesLoading(true);
    setSalesData(null);
    try {
      const response = await fetchSkuSalesSummary(artno, mcode || void 0);
      setSalesData(response);
      if (!response.success) {
        message.warning(response.message || "Failed to fetch sales data");
      }
    } catch (error2) {
      console.error("Error fetching sales summary:", error2);
      message.error("Failed to fetch sales summary");
    } finally {
      setSalesLoading(false);
    }
  };
  const handleCloseSalesModal = () => {
    setSalesModalVisible(false);
    setSalesSelectedSku(null);
    setSalesData(null);
    setSalesLoading(false);
    setSalesSearchText("");
  };
  const generateBarcodePDF = async () => {
    if (selectedRows.length === 0) {
      message.error("No barcodes to generate");
      return;
    }
    setGeneratingPDF(true);
    try {
      const pdf = new jsPDF("p", "mm", "a4");
      const pageWidth = 210;
      const pageHeight = 297;
      const barcodeWidth = 60;
      const barcodeHeight = 50;
      const margin = 10;
      const gap = 5;
      const availableWidth = pageWidth - 2 * margin;
      const availableHeight = pageHeight - 2 * margin;
      const cols = 2;
      const colWidth = (availableWidth - gap) / cols;
      const rowsPerPage = Math.floor((availableHeight + gap) / (barcodeHeight + gap));
      const barcodesPerPage = cols * rowsPerPage;
      let currentPage2 = 0;
      for (let i = 0; i < selectedRows.length; i++) {
        const item = selectedRows[i];
        if (i % barcodesPerPage === 0) {
          if (currentPage2 > 0) {
            pdf.addPage();
          }
          currentPage2++;
        }
        const indexOnPage = i % barcodesPerPage;
        const row = Math.floor(indexOnPage / cols);
        const col = indexOnPage % cols;
        const x = margin + col * (colWidth + gap);
        const y = margin + row * (barcodeHeight + gap);
        pdf.setDrawColor(200, 200, 200);
        pdf.setLineWidth(0.1);
        pdf.rect(x, y, barcodeWidth, barcodeHeight);
        if (item.MCode) {
          const canvas = document.createElement("canvas");
          const canvasWidth = 400;
          const canvasHeight = 60;
          canvas.width = canvasWidth;
          canvas.height = canvasHeight;
          try {
            JsBarcode(canvas, item.MCode, {
              format: "CODE128",
              width: 2,
              height: 50,
              fontSize: 12,
              margin: 5,
              background: "#ffffff",
              lineColor: "#000000",
              text: item.MCode
              // Show MCode in barcode tooltip
            });
            const barcodeImgData = canvas.toDataURL("image/png");
            pdf.addImage(barcodeImgData, "PNG", x + 2, y + 2, barcodeWidth - 4, 15);
          } catch (error2) {
            console.error("Error generating barcode for", item.MCode, error2);
          }
        }
        pdf.setFontSize(8);
        pdf.setFont("helvetica", "bold");
        const description = item["ART_NO DESCRIPTION"] || "N/A";
        const maxLineLength = 22;
        const words = description.split(" ");
        let line1 = "";
        let line2 = "";
        for (const word of words) {
          if ((line1 + word).length <= maxLineLength) {
            line1 += (line1 ? " " : "") + word;
          } else {
            if ((line2 + word).length <= maxLineLength) {
              line2 += (line2 ? " " : "") + word;
            } else {
              if (line2.length === 0) {
                line2 = word.substring(0, maxLineLength - 3) + "...";
              } else {
                line2 = line2 + "...";
              }
              break;
            }
          }
        }
        if (line1.length > maxLineLength) {
          line1 = line1.substring(0, maxLineLength - 3) + "...";
          line2 = "";
        }
        pdf.text(line1, x + 2, y + 22);
        if (line2) {
          pdf.text(line2, x + 2, y + 26);
        }
        const sizeColorY = line2 ? y + 31 : y + 27;
        pdf.setFontSize(9);
        pdf.setFont("helvetica", "normal");
        const sizeText = `Size: ${item.Size || "N/A"}`;
        const colorText = `Color: ${item.Color || "N/A"}`;
        pdf.text(sizeText, x + 2, sizeColorY);
        pdf.text(colorText, x + 32, sizeColorY);
        const priceY = sizeColorY + 5;
        pdf.setFontSize(9);
        pdf.setFont("helvetica", "bold");
        const netPrice = `Net: RM ${item.Net_Selling_Price ? item.Net_Selling_Price.toFixed(2) : "N/A"}`;
        pdf.text(netPrice, x + 2, priceY);
        if (item.RSP) {
          const rspText = `RSP: RM ${item.RSP.toFixed(2)}`;
          pdf.setFont("helvetica", "normal");
          pdf.setFontSize(9);
          pdf.text(rspText, x + 32, priceY);
          const rspWidth = pdf.getTextWidth(rspText);
          pdf.setDrawColor(0, 0, 0);
          pdf.setLineWidth(0.2);
          pdf.line(x + 32, priceY - 1, x + 32 + rspWidth, priceY - 1);
        }
        pdf.setFontSize(7);
        pdf.setFont("helvetica", "normal");
        pdf.text(item.Art_No || "N/A", x + 2, y + 47);
      }
      const timestamp = (/* @__PURE__ */ new Date()).toISOString().replace(/[:.]/g, "-").slice(0, -5);
      const filename = `barcodes_${selectedRows.length}_items_${timestamp}.pdf`;
      pdf.save(filename);
      message.success(`PDF generated successfully: ${filename}`);
    } catch (error2) {
      console.error("Error generating PDF:", error2);
      message.error("Failed to generate PDF");
    } finally {
      setGeneratingPDF(false);
    }
  };
  const renderBarcodePreview = () => {
    if (selectedRows.length === 0) return null;
    return /* @__PURE__ */ jsx(
      "div",
      {
        ref: barcodePreviewRef,
        style: {
          display: "grid",
          gridTemplateColumns: "repeat(2, 1fr)",
          gap: "10px",
          padding: "20px",
          backgroundColor: "#ffffff",
          maxHeight: "500px",
          overflowY: "auto"
        },
        children: selectedRows.map((item, index) => /* @__PURE__ */ jsxs(
          "div",
          {
            style: {
              width: "60mm",
              height: "50mm",
              border: "1px solid #ddd",
              padding: "4px",
              backgroundColor: "#ffffff",
              display: "flex",
              flexDirection: "column",
              justifyContent: "space-between",
              fontSize: "10px",
              fontFamily: "Arial, sans-serif"
            },
            children: [
              /* @__PURE__ */ jsx("div", { style: { textAlign: "center", marginBottom: "4px" }, children: item.MCode ? /* @__PURE__ */ jsx(
                "canvas",
                {
                  ref: (canvas) => {
                    if (canvas && item.MCode) {
                      try {
                        JsBarcode(canvas, item.MCode, {
                          format: "CODE128",
                          width: 1.5,
                          height: 30,
                          fontSize: 8,
                          margin: 2,
                          background: "#ffffff",
                          lineColor: "#000000",
                          text: item.MCode
                          // Show MCode in barcode tooltip
                        });
                      } catch (error2) {
                        console.error("Error generating barcode preview for", item.MCode, error2);
                      }
                    }
                  },
                  style: { maxWidth: "100%", height: "auto" }
                }
              ) : /* @__PURE__ */ jsx("div", { style: { height: "30px", backgroundColor: "#f5f5f5", display: "flex", alignItems: "center", justifyContent: "center", fontSize: "8px" }, children: "No MCode" }) }),
              /* @__PURE__ */ jsxs("div", { style: { flex: 1, display: "flex", flexDirection: "column", justifyContent: "space-between" }, children: [
                /* @__PURE__ */ jsx("div", { style: { fontWeight: "bold", fontSize: "8px", marginBottom: "4px", lineHeight: "1.2", wordWrap: "break-word" }, children: (() => {
                  const description = item["ART_NO DESCRIPTION"] || "N/A";
                  const maxLineLength = 22;
                  const words = description.split(" ");
                  let line1 = "";
                  let line2 = "";
                  for (const word of words) {
                    if ((line1 + word).length <= maxLineLength) {
                      line1 += (line1 ? " " : "") + word;
                    } else {
                      if ((line2 + word).length <= maxLineLength) {
                        line2 += (line2 ? " " : "") + word;
                      } else {
                        if (line2.length === 0) {
                          line2 = word.substring(0, maxLineLength - 3) + "...";
                        } else {
                          line2 = line2 + "...";
                        }
                        break;
                      }
                    }
                  }
                  if (line1.length > maxLineLength) {
                    line1 = line1.substring(0, maxLineLength - 3) + "...";
                    line2 = "";
                  }
                  return /* @__PURE__ */ jsxs(Fragment, { children: [
                    /* @__PURE__ */ jsx("div", { children: line1 }),
                    line2 && /* @__PURE__ */ jsx("div", { children: line2 })
                  ] });
                })() }),
                /* @__PURE__ */ jsxs("div", { style: { display: "flex", justifyContent: "space-between", fontSize: "9px", marginBottom: "4px" }, children: [
                  /* @__PURE__ */ jsxs("span", { children: [
                    "Size: ",
                    item.Size || "N/A"
                  ] }),
                  /* @__PURE__ */ jsxs("span", { children: [
                    "Color: ",
                    item.Color || "N/A"
                  ] })
                ] }),
                /* @__PURE__ */ jsxs("div", { style: { display: "flex", justifyContent: "space-between", fontSize: "9px", marginBottom: "4px" }, children: [
                  /* @__PURE__ */ jsxs("span", { style: { fontWeight: "bold" }, children: [
                    "Net: RM ",
                    item.Net_Selling_Price ? item.Net_Selling_Price.toFixed(2) : "N/A"
                  ] }),
                  item.RSP && /* @__PURE__ */ jsxs("span", { style: { textDecoration: "line-through", color: "#999" }, children: [
                    "RSP: RM ",
                    item.RSP.toFixed(2)
                  ] })
                ] }),
                /* @__PURE__ */ jsx("div", { style: { fontSize: "7px", color: "#666", textAlign: "left" }, children: item.Art_No || "N/A" })
              ] })
            ]
          },
          `${item.sku_item_code}-${index}`
        ))
      }
    );
  };
  useEffect(() => {
    if (isScannerModalVisible) {
      const timer = setTimeout(() => {
        initializeScanner();
      }, 100);
      return () => clearTimeout(timer);
    } else {
      stopScanner();
    }
  }, [isScannerModalVisible]);
  useEffect(() => {
    if (triggerSearch) {
      if (searchValue.trim()) {
        handleSearch();
      }
      setTriggerSearch(false);
    }
  }, [triggerSearch, searchValue]);
  return /* @__PURE__ */ jsx(ConfigProvider, { locale: enUS, children: /* @__PURE__ */ jsxs(PageContainer, { title: "SKU Checker", children: [
    /* @__PURE__ */ jsxs(ProCard, { direction: "column", ghost: true, gutter: [0, 16], children: [
      /* @__PURE__ */ jsx(ProCard, { children: /* @__PURE__ */ jsxs(Space, { direction: "vertical", style: { width: "100%" }, size: "large", children: [
        /* @__PURE__ */ jsxs(Row, { gutter: [16, 24], style: { width: "100%" }, children: [
          /* @__PURE__ */ jsx(Col, { xs: 24, md: 12, children: /* @__PURE__ */ jsxs(Space, { direction: "vertical", style: { width: "100%" }, size: "middle", children: [
            /* @__PURE__ */ jsx(Text, { strong: true, children: "Search By:" }),
            /* @__PURE__ */ jsxs(
              Radio.Group,
              {
                buttonStyle: "solid",
                value: searchType,
                onChange: (e) => {
                  const newSearchType = e.target.value;
                  setSearchType(newSearchType);
                },
                style: { width: "100%" },
                children: [
                  /* @__PURE__ */ jsx(Radio.Button, { value: "artno", children: "Art No" }),
                  /* @__PURE__ */ jsx(Radio.Button, { value: "mcode", children: "MCode" }),
                  /* @__PURE__ */ jsx(Radio.Button, { value: "sku_item_code", children: "ARMSCode" }),
                  /* @__PURE__ */ jsx(Radio.Button, { value: "description", children: "Description" })
                ]
              }
            )
          ] }) }),
          /* @__PURE__ */ jsx(Col, { xs: 24, md: 12, children: /* @__PURE__ */ jsxs(Space, { direction: "vertical", style: { width: "100%" }, size: "middle", children: [
            /* @__PURE__ */ jsx(Text, { strong: true, children: "Search Scope:" }),
            /* @__PURE__ */ jsxs(
              Radio.Group,
              {
                buttonStyle: "solid",
                value: searchScope,
                onChange: (e) => setSearchScope(e.target.value),
                disabled: searchScopeDisabled,
                style: { width: "100%" },
                children: [
                  /* @__PURE__ */ jsx(Radio.Button, { value: "parent", children: "Parent Code Only" }),
                  /* @__PURE__ */ jsx(Radio.Button, { value: "all", children: "All Codes (Parent & Child)" })
                ]
              }
            )
          ] }) })
        ] }),
        isCodeSearchType() ? /* @__PURE__ */ jsx(
          Input.TextArea,
          {
            rows: 3,
            placeholder: getSearchPlaceholder(),
            value: searchValue,
            onChange: (e) => setSearchValue(e.target.value),
            style: { width: "100%" }
          }
        ) : /* @__PURE__ */ jsx(
          Input,
          {
            placeholder: getSearchPlaceholder(),
            value: searchValue,
            onChange: (e) => setSearchValue(e.target.value),
            onPressEnter: () => handleSearch(),
            style: { width: "100%" }
          }
        ),
        isCodeSearchType() && /* @__PURE__ */ jsxs(
          Checkbox,
          {
            checked: useWildcard,
            onChange: (e) => setUseWildcard(e.target.checked),
            children: [
              "Use Wildcard Search (e.g., %value%) for ",
              searchTypeLabels[searchType]
            ]
          }
        ),
        /* @__PURE__ */ jsxs(Row, { gutter: [16, 16], style: { width: "100%" }, children: [
          /* @__PURE__ */ jsx(Col, { xs: 24, sm: 8, md: 6, children: /* @__PURE__ */ jsx(
            Button,
            {
              type: "primary",
              icon: /* @__PURE__ */ jsx(SearchOutlined, {}),
              onClick: () => handleSearch(),
              loading,
              style: { width: "100%" },
              children: "Search"
            }
          ) }),
          /* @__PURE__ */ jsx(Col, { xs: 24, sm: 8, md: 6, children: /* @__PURE__ */ jsx(
            Button,
            {
              onClick: handleReset,
              style: {
                width: "100%",
                backgroundColor: "#ff4d4f",
                color: "white",
                borderColor: "#ff4d4f"
              },
              children: "Reset"
            }
          ) }),
          /* @__PURE__ */ jsx(Col, { xs: 24, sm: 8, md: 6, children: /* @__PURE__ */ jsx(
            Button,
            {
              icon: /* @__PURE__ */ jsx(DownloadOutlined, {}),
              onClick: handleExport,
              style: {
                backgroundColor: "#217346",
                color: "white",
                borderColor: "#217346",
                width: "100%"
              },
              disabled: !searchValue.trim(),
              children: "Export to Excel"
            }
          ) }),
          /* @__PURE__ */ jsx(Col, { xs: 24, sm: 8, md: 6, children: /* @__PURE__ */ jsx(
            Button,
            {
              icon: /* @__PURE__ */ jsx(CameraOutlined, {}),
              onClick: handleOpenScanner,
              disabled: !!(scannerError && scannerError.startsWith("Could not load")),
              style: { width: "100%" },
              children: "Scan Barcode"
            }
          ) }),
          /* @__PURE__ */ jsx(Col, { xs: 24, sm: 8, md: 6, children: /* @__PURE__ */ jsxs(
            Button,
            {
              icon: /* @__PURE__ */ jsx(QrcodeOutlined, {}),
              onClick: handleOpenBarcodeGenerator,
              disabled: selectedRows.length === 0,
              style: {
                width: "100%",
                backgroundColor: "#722ed1",
                color: "white",
                borderColor: "#722ed1"
              },
              children: [
                "Generate Barcode (",
                selectedRows.length,
                ")"
              ]
            }
          ) })
        ] })
      ] }) }),
      loading && /* @__PURE__ */ jsx("div", { style: { textAlign: "center", padding: "20px" }, children: /* @__PURE__ */ jsx(Spin, { tip: "Loading SKU details...", size: "large" }) }),
      error && !loading && /* @__PURE__ */ jsx(Alert, { message: error, type: "error", showIcon: true, style: { marginBottom: 16 } }),
      !loading && searchPerformed && searchResults.length === 0 && !error && /* @__PURE__ */ jsx(Empty, { description: "No SKU found matching your criteria. Try different search terms or options." }),
      searchResults.length > 0 && !loading && /* @__PURE__ */ jsx(
        ProTable,
        {
          headerTitle: "Search Results",
          columns,
          dataSource: filteredDisplayData,
          rowKey: "sku_item_code",
          search: false,
          rowSelection: {
            type: "checkbox",
            selectedRowKeys,
            onChange: (newSelectedRowKeys, newSelectedRows) => {
              setSelectedRowKeys(newSelectedRowKeys);
              setSelectedRows(newSelectedRows);
            },
            getCheckboxProps: (record) => ({
              disabled: !record.Art_No,
              // Disable selection if no Art_No for barcode generation
              name: record.sku_item_code
            })
          },
          toolBarRender: () => [
            /* @__PURE__ */ jsx(
              Input.Search,
              {
                placeholder: "Search in results...",
                allowClear: true,
                value: tableSearchText,
                onChange: (e) => setTableSearchText(e.target.value),
                onSearch: (value) => setTableSearchText(value),
                style: { width: 250 }
              },
              "table-search"
            )
          ],
          pagination: {
            current: currentPage,
            pageSize: currentPageSize,
            total: filteredDisplayData.length,
            // Use client-side filtered data length
            showSizeChanger: true,
            pageSizeOptions: ["10", "20", "50", "100", "200", "500"],
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              setCurrentPageSize(pageSize);
            },
            size: "small"
          },
          size: "small",
          scroll: { x: "max-content", y: 400 },
          tableStyle: {
            fontSize: "12px"
          },
          onRow: (record) => {
            return {
              onClick: () => {
                setSelectedSku(record);
              }
            };
          },
          rowClassName: (record) => record.sku_item_code === selectedSku?.sku_item_code ? "ant-table-row-selected" : ""
        }
      ),
      renderImageSection(selectedSku),
      renderSkuDetailsSection(selectedSku)
    ] }),
    /* @__PURE__ */ jsx(
      ExportHistory,
      {
        reportNameFilter: "SKU Checker"
      }
    ),
    /* @__PURE__ */ jsxs(
      Modal,
      {
        title: "Scan Barcode",
        open: isScannerModalVisible,
        onCancel: handleModalClose,
        footer: null,
        destroyOnClose: true,
        width: "80%",
        style: { maxWidth: "600px" },
        children: [
          scannerInitializing && /* @__PURE__ */ jsx(Spin, { tip: "Initializing camera...", children: /* @__PURE__ */ jsx("div", { style: { height: "50px", display: "flex", alignItems: "center", justifyContent: "center" }, children: "Initializing..." }) }),
          scannerError && !scannerInitializing && /* @__PURE__ */ jsx(Alert, { message: "Scanner Error", description: scannerError, type: "error", showIcon: true, style: { marginBottom: "10px" } }),
          /* @__PURE__ */ jsx("div", { ref: interactiveRef, style: {
            position: "relative",
            width: "100%",
            paddingTop: "75%",
            // 4:3 aspect ratio
            backgroundColor: "#000",
            overflow: "hidden"
          }, children: isScannerModalVisible && !scannerInitializing && !scannerError && /* @__PURE__ */ jsxs("div", { style: {
            position: "absolute",
            top: 0,
            right: 0,
            bottom: 0,
            left: 0,
            zIndex: 2
          }, children: [
            /* @__PURE__ */ jsx("div", { style: {
              position: "absolute",
              left: "10%",
              right: "10%",
              top: "20%",
              bottom: "20%",
              border: "2px solid rgba(255, 255, 255, 0.7)",
              borderRadius: "8px",
              boxShadow: "0 0 0 9999px rgba(0, 0, 0, 0.6)"
            } }),
            /* @__PURE__ */ jsx("div", { style: {
              position: "absolute",
              left: "5%",
              right: "5%",
              top: "50%",
              height: "2px",
              background: "red",
              boxShadow: "0 0 5px red",
              zIndex: 10,
              animation: "scanLaserAnimation 2s infinite linear"
            } })
          ] }) })
        ]
      }
    ),
    /* @__PURE__ */ jsx("style", { children: `@keyframes scanLaserAnimation {
              0% { transform: translateY(-50px); }
              50% { transform: translateY(50px); }
              100% { transform: translateY(-50px); }
          }
          .drawingBuffer, video {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              object-fit: cover;
          }
          
          /* Responsive table improvements */
          @media (max-width: 768px) {
            .ant-pro-table .ant-table-thead > tr > th {
              font-size: 11px !important;
              padding: 8px 4px !important;
              white-space: nowrap;
            }
            .ant-pro-table .ant-table-tbody > tr > td {
              font-size: 11px !important;
              padding: 6px 4px !important;
            }
            .ant-tag {
              font-size: 10px !important;
              padding: 0 4px !important;
              line-height: 16px !important;
            }
          }
          
          @media (max-width: 576px) {
            .ant-pro-table .ant-table-thead > tr > th {
              font-size: 10px !important;
              padding: 6px 2px !important;
            }
            .ant-pro-table .ant-table-tbody > tr > td {
              font-size: 10px !important;
              padding: 4px 2px !important;
            }
            .ant-tag {
              font-size: 9px !important;
              padding: 0 2px !important;
              line-height: 14px !important;
            }
          }
          ` }),
    /* @__PURE__ */ jsx(
      Modal,
      {
        title: "Large Result Set Detected",
        open: isLargeResultModalVisible,
        onCancel: () => setIsLargeResultModalVisible(false),
        footer: [
          /* @__PURE__ */ jsx(Button, { onClick: () => setIsLargeResultModalVisible(false), children: "Continue Viewing" }, "cancel"),
          /* @__PURE__ */ jsx(Button, { type: "primary", icon: /* @__PURE__ */ jsx(DownloadOutlined, {}), onClick: handleExportFromModal, children: "Export to Excel" }, "export")
        ],
        width: 600,
        children: /* @__PURE__ */ jsxs(Space, { direction: "vertical", size: "middle", style: { width: "100%" }, children: [
          /* @__PURE__ */ jsx(
            Alert,
            {
              message: "Performance Notice",
              description: largeResultMessage,
              type: "warning",
              showIcon: true
            }
          ),
          /* @__PURE__ */ jsx("div", { children: /* @__PURE__ */ jsxs(Text, { children: [
            "For the best performance and to access all results, we recommend using the Export to Excel feature. This will generate a complete file with all ",
            apiTotalResults.toLocaleString(),
            " results that you can download once ready."
          ] }) }),
          /* @__PURE__ */ jsxs("div", { children: [
            /* @__PURE__ */ jsx(Text, { strong: true, children: "Options:" }),
            /* @__PURE__ */ jsxs("ul", { style: { marginTop: 8, marginBottom: 0 }, children: [
              /* @__PURE__ */ jsxs("li", { children: [
                /* @__PURE__ */ jsx(Text, { strong: true, children: "Continue Viewing:" }),
                " Browse the limited results displayed in the table above"
              ] }),
              /* @__PURE__ */ jsxs("li", { children: [
                /* @__PURE__ */ jsx(Text, { strong: true, children: "Export to Excel:" }),
                " Generate a complete file with all results for download"
              ] })
            ] })
          ] })
        ] })
      }
    ),
    /* @__PURE__ */ jsx(
      SkuImageGallery,
      {
        artno: currentArtno,
        visible: imageGalleryVisible,
        onClose: handleCloseImageGallery,
        onImagesUpdated: handleImagesUpdated
      }
    ),
    /* @__PURE__ */ jsx(
      Modal,
      {
        title: `Generate Barcodes (${selectedRows.length} items selected)`,
        open: barcodeModalVisible,
        onCancel: handleCloseBarcodeModal,
        footer: [
          /* @__PURE__ */ jsx(Button, { onClick: handleCloseBarcodeModal, children: "Cancel" }, "cancel"),
          /* @__PURE__ */ jsx(
            Button,
            {
              type: "primary",
              icon: /* @__PURE__ */ jsx(DownloadOutlined, {}),
              onClick: generateBarcodePDF,
              loading: generatingPDF,
              disabled: selectedRows.length === 0,
              children: "Generate PDF"
            },
            "generate"
          )
        ],
        destroyOnClose: true,
        width: "90%",
        style: { maxWidth: "800px", top: 20 },
        children: /* @__PURE__ */ jsxs(Space, { direction: "vertical", style: { width: "100%" }, size: "middle", children: [
          /* @__PURE__ */ jsx(
            Alert,
            {
              message: "Barcode Preview",
              description: `${selectedRows.length} barcode(s) will be generated in Code128 format. Layout: 60mm x 50mm per barcode, 2 columns per page.`,
              type: "info",
              showIcon: true
            }
          ),
          generatingPDF && /* @__PURE__ */ jsxs("div", { style: { textAlign: "center", padding: "20px" }, children: [
            /* @__PURE__ */ jsx(Spin, { size: "large" }),
            /* @__PURE__ */ jsx("div", { style: { marginTop: 10 }, children: "Generating PDF..." })
          ] }),
          !generatingPDF && /* @__PURE__ */ jsx("div", { style: { maxHeight: "60vh", overflowY: "auto", border: "1px solid #f0f0f0", borderRadius: "6px" }, children: renderBarcodePreview() })
        ] })
      }
    ),
    /* @__PURE__ */ jsx(
      Modal,
      {
        title: salesSelectedSku?.MCode ? `Sales Summary - ${salesSelectedSku?.Art_No || "N/A"} (${salesSelectedSku?.MCode})` : `Sales Summary - ${salesSelectedSku?.Art_No || "N/A"} (All Child Codes)`,
        open: salesModalVisible,
        onCancel: handleCloseSalesModal,
        footer: [
          /* @__PURE__ */ jsx(Button, { onClick: handleCloseSalesModal, children: "Close" }, "close")
        ],
        width: 800,
        destroyOnClose: true,
        children: /* @__PURE__ */ jsxs(Space, { direction: "vertical", style: { width: "100%" }, size: "middle", children: [
          salesLoading && /* @__PURE__ */ jsxs("div", { style: { textAlign: "center", padding: "20px" }, children: [
            /* @__PURE__ */ jsx(Spin, { size: "large" }),
            /* @__PURE__ */ jsx("div", { style: { marginTop: 10 }, children: "Loading sales data..." })
          ] }),
          !salesLoading && salesData && /* @__PURE__ */ jsx(Fragment, { children: salesData.success ? /* @__PURE__ */ jsxs(Fragment, { children: [
            /* @__PURE__ */ jsxs(Descriptions, { title: "Sales Overview", bordered: true, size: "small", column: 1, children: [
              /* @__PURE__ */ jsx(Descriptions.Item, { label: "Article Number", children: salesData.artno }),
              /* @__PURE__ */ jsx(Descriptions.Item, { label: "Search Type", children: !salesData.mcode || salesData.mcode === "ALL_CHILDREN" ? /* @__PURE__ */ jsx(Tag, { color: "blue", children: "Parent Code (All Child Variants)" }) : /* @__PURE__ */ jsxs(Tag, { color: "green", children: [
                "Specific SKU (",
                salesData.mcode,
                ")"
              ] }) }),
              /* @__PURE__ */ jsx(Descriptions.Item, { label: "Total Quantity Sold", children: /* @__PURE__ */ jsx(Text, { strong: true, style: { color: "#1890ff", fontSize: "16px" }, children: salesData.total_qty_sold.toLocaleString() }) }),
              /* @__PURE__ */ jsx(Descriptions.Item, { label: "Branches with Sales", children: /* @__PURE__ */ jsx(Text, { strong: true, children: salesData.branch_count }) }),
              /* @__PURE__ */ jsx(Descriptions.Item, { label: "Sales Period", children: salesData.first_sale_date && salesData.last_sale_date ? /* @__PURE__ */ jsxs(Text, { children: [
                salesData.first_sale_date,
                " to ",
                salesData.last_sale_date
              ] }) : /* @__PURE__ */ jsx(Text, { type: "secondary", children: "N/A" }) })
            ] }),
            salesData.branch_details && salesData.branch_details.length > 0 && /* @__PURE__ */ jsxs("div", { children: [
              /* @__PURE__ */ jsxs("div", { style: { display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "12px" }, children: [
                /* @__PURE__ */ jsx(Text, { strong: true, style: { fontSize: "16px" }, children: "Branch Breakdown" }),
                /* @__PURE__ */ jsx(
                  Input,
                  {
                    placeholder: "Search branches...",
                    value: salesSearchText,
                    onChange: (e) => setSalesSearchText(e.target.value),
                    allowClear: true,
                    style: { width: 250 },
                    prefix: /* @__PURE__ */ jsx(SearchOutlined, {})
                  }
                )
              ] }),
              /* @__PURE__ */ jsx(
                ProTable,
                {
                  columns: [
                    {
                      title: "Branch Code",
                      dataIndex: "branch_code",
                      key: "branch_code",
                      width: 120,
                      sorter: (a, b) => a.branch_code.localeCompare(b.branch_code)
                    },
                    {
                      title: "Branch Name",
                      dataIndex: "branch_name",
                      key: "branch_name",
                      ellipsis: true,
                      sorter: (a, b) => a.branch_name.localeCompare(b.branch_name)
                    },
                    {
                      title: "Qty Sold",
                      dataIndex: "total_qty",
                      // Changed from total_qty_sold to total_qty
                      key: "total_qty",
                      // Changed from total_qty_sold to total_qty
                      width: 100,
                      align: "right",
                      sorter: (a, b) => a.total_qty - b.total_qty,
                      // Changed from total_qty_sold to total_qty
                      render: (value) => /* @__PURE__ */ jsx(Text, { strong: true, style: { color: "#1890ff" }, children: value.toLocaleString() })
                    },
                    {
                      title: "First Sale",
                      dataIndex: "first_sale_date",
                      key: "first_sale_date",
                      width: 100,
                      sorter: (a, b) => (a.first_sale_date || "").localeCompare(b.first_sale_date || ""),
                      render: (value) => value || "N/A"
                    },
                    {
                      title: "Last Sale",
                      dataIndex: "last_sale_date",
                      key: "last_sale_date",
                      width: 100,
                      sorter: (a, b) => (a.last_sale_date || "").localeCompare(b.last_sale_date || ""),
                      render: (value) => value || "N/A"
                    },
                    {
                      title: "Total Sales (RM)",
                      dataIndex: "total_sales",
                      key: "total_sales",
                      width: 120,
                      align: "right",
                      sorter: (a, b) => a.total_sales - b.total_sales,
                      render: (value) => /* @__PURE__ */ jsxs(Text, { strong: true, style: { color: "#52c41a" }, children: [
                        "RM ",
                        value.toLocaleString(void 0, { minimumFractionDigits: 2, maximumFractionDigits: 2 })
                      ] })
                    }
                  ],
                  dataSource: salesData.branch_details.filter(
                    (branch) => !salesSearchText || branch.branch_code.toLowerCase().includes(salesSearchText.toLowerCase()) || branch.branch_name.toLowerCase().includes(salesSearchText.toLowerCase())
                  ),
                  rowKey: "branch_code",
                  pagination: {
                    pageSize: 150,
                    showSizeChanger: true,
                    pageSizeOptions: ["50", "100", "150", "200", "500"],
                    showQuickJumper: true,
                    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} branches`,
                    size: "small"
                  },
                  scroll: { y: 400 },
                  size: "small",
                  search: false,
                  toolBarRender: false,
                  options: false
                }
              )
            ] })
          ] }) : /* @__PURE__ */ jsx(
            Alert,
            {
              message: "No Sales Data",
              description: salesData.message || "No sales records found for this SKU.",
              type: "info",
              showIcon: true
            }
          ) })
        ] })
      }
    ),
    /* @__PURE__ */ jsx(
      QOHModal,
      {
        visible: qohModalVisible,
        onClose: handleCloseQOHModal,
        artno: qohSelectedSku?.Art_No,
        mcode: qohSelectedSku?.MCode,
        skuDescription: qohSelectedSku?.["ART_NO DESCRIPTION"]
      }
    )
  ] }) });
};
export default SkuCheckerPage;
