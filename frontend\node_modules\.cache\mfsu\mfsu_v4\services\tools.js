"use strict";
import { request } from "@umijs/max";
export async function fetchSkuSalesSummary(artno, mcode) {
  try {
    const params = { artno };
    if (mcode) {
      params.mcode = mcode;
    }
    const response = await request("/api/tools/sku-checker/sales-summary", {
      method: "GET",
      params
    });
    if (typeof response.success === "undefined") {
      console.error("fetchSkuSalesSummary: API response missing success field", response);
      return {
        artno,
        mcode,
        total_qty_sold: 0,
        total_sales_amount: 0,
        // Added total_sales_amount field
        branch_count: 0,
        branch_details: [],
        success: false,
        message: "Invalid response format from server."
      };
    }
    return response;
  } catch (error) {
    console.error("fetchSkuSalesSummary error:", error);
    let errorMessage = "Failed to fetch sales summary from server.";
    if (error?.response?.data?.detail) {
      if (typeof error.response.data.detail === "string") {
        errorMessage = error.response.data.detail;
      }
    } else if (error?.message) {
      errorMessage = error.message;
    }
    return {
      artno,
      mcode,
      total_qty_sold: 0,
      total_sales_amount: 0,
      // Added total_sales_amount field
      branch_count: 0,
      branch_details: [],
      success: false,
      message: errorMessage
    };
  }
}
export async function fetchSkuDetails(params) {
  try {
    const response = await request("/api/tools/sku-checker", {
      method: "GET",
      params
    });
    if (typeof response.success === "undefined") {
      console.error("fetchSkuDetails: API response missing success field", response);
      return {
        data: [],
        current_batch_count: 0,
        grand_total: 0,
        success: false,
        error: "Invalid response format from server."
      };
    }
    return response;
  } catch (error) {
    console.error("fetchSkuDetails error:", error);
    let errorMessage = "Failed to fetch SKU details from server.";
    if (error?.response?.data?.detail) {
      if (typeof error.response.data.detail === "string") {
        errorMessage = error.response.data.detail;
      } else if (Array.isArray(error.response.data.detail) && error.response.data.detail.length > 0 && error.response.data.detail[0].msg) {
        errorMessage = error.response.data.detail.map((d) => `${d.loc.join(".")} - ${d.msg}`).join("; ");
      } else if (typeof error.response.data.detail === "object") {
        errorMessage = JSON.stringify(error.response.data.detail);
      }
    } else if (error?.message) {
      errorMessage = error.message;
    }
    return {
      data: [],
      current_batch_count: 0,
      grand_total: 0,
      success: false,
      error: errorMessage
    };
  }
}
export async function getPriceInfo(params) {
  try {
    const response = await request("/api/tools/price-check", {
      method: "POST",
      data: params
    });
    return response;
  } catch (error) {
    console.error("getPriceInfo error:", error);
    return {
      success: false,
      message: error?.response?.data?.detail || error?.message || "Failed to fetch price information."
    };
  }
}
export async function getPriceInfoEnhanced(params) {
  try {
    const response = await request("/api/tools/price-check-enhanced", {
      method: "POST",
      data: params
    });
    return response;
  } catch (error) {
    console.error("getPriceInfoEnhanced error:", error);
    return {
      data: [],
      search_type: params.search_type,
      search_value: params.search_value,
      total_variants: 0,
      success: false,
      message: error?.response?.data?.detail || error?.message || "Failed to fetch price information."
    };
  }
}
export async function getPriceInfoWithBranches(params) {
  try {
    const response = await request("/api/tools/price-check-with-branches", {
      method: "POST",
      data: params
    });
    return response;
  } catch (error) {
    console.error("getPriceInfoWithBranches error:", error);
    return {
      data: [],
      branch_prices: [],
      search_type: params.search_type,
      search_value: params.search_value,
      total_variants: 0,
      success: false,
      message: error?.response?.data?.detail || error?.message || "Failed to fetch price information with branch prices."
    };
  }
}
export async function exportSkuChecker(params) {
  try {
    const response = await request("/api/tools/sku-checker/export", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      data: params
    });
    return response;
  } catch (error) {
    console.error("exportSkuChecker error:", error);
    let errorMessage = "Failed to trigger SKU Checker export.";
    if (error?.response?.data?.detail) {
      if (typeof error.response.data.detail === "string") {
        errorMessage = error.response.data.detail;
      } else if (Array.isArray(error.response.data.detail) && error.response.data.detail.length > 0 && error.response.data.detail[0].msg) {
        errorMessage = error.response.data.detail.map((d) => `${d.loc.join(".")} - ${d.msg}`).join("; ");
      }
    } else if (error?.message) {
      errorMessage = error.message;
    }
    return {
      success: false,
      message: errorMessage
    };
  }
}
export async function getSkuImages(artno) {
  try {
    const response = await request(`/api/tools/sku-images/${artno}`, {
      method: "GET"
    });
    return response;
  } catch (error) {
    console.error("getSkuImages error:", error);
    let errorMessage = "Failed to fetch SKU images.";
    if (error?.response?.data?.detail) {
      errorMessage = typeof error.response.data.detail === "string" ? error.response.data.detail : JSON.stringify(error.response.data.detail);
    } else if (error?.message) {
      errorMessage = error.message;
    }
    return {
      success: false,
      images: [],
      total_count: 0,
      message: errorMessage
    };
  }
}
export async function searchGoogleImages(params) {
  try {
    const response = await request("/api/tools/sku-images/search", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      data: params
    });
    return response;
  } catch (error) {
    console.error("searchGoogleImages error:", error);
    let errorMessage = "Failed to search Google images.";
    if (error?.response?.data?.detail) {
      errorMessage = typeof error.response.data.detail === "string" ? error.response.data.detail : JSON.stringify(error.response.data.detail);
    } else if (error?.message) {
      errorMessage = error.message;
    }
    return {
      success: false,
      results: [],
      total_results: 0,
      message: errorMessage
    };
  }
}
export async function saveImageFromUrl(params) {
  try {
    const response = await request("/api/tools/sku-images/save-from-url", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      data: params
    });
    return response;
  } catch (error) {
    console.error("saveImageFromUrl error:", error);
    let errorMessage = "Failed to save image from URL.";
    if (error?.response?.data?.detail) {
      errorMessage = typeof error.response.data.detail === "string" ? error.response.data.detail : JSON.stringify(error.response.data.detail);
    } else if (error?.message) {
      errorMessage = error.message;
    }
    return {
      success: false,
      message: errorMessage
    };
  }
}
export async function uploadImage(params) {
  try {
    const response = await request("/api/tools/sku-images/upload", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      data: params
    });
    return response;
  } catch (error) {
    console.error("uploadImage error:", error);
    let errorMessage = "Failed to upload image.";
    if (error?.response?.data?.detail) {
      errorMessage = typeof error.response.data.detail === "string" ? error.response.data.detail : JSON.stringify(error.response.data.detail);
    } else if (error?.message) {
      errorMessage = error.message;
    }
    return {
      success: false,
      message: errorMessage
    };
  }
}
export async function bulkUploadImages(params) {
  try {
    const response = await request("/api/tools/sku-images/bulk-upload", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      data: params
    });
    return response;
  } catch (error) {
    console.error("bulkUploadImages error:", error);
    let errorMessage = "Failed to upload images.";
    if (error?.response?.data?.detail) {
      errorMessage = typeof error.response.data.detail === "string" ? error.response.data.detail : JSON.stringify(error.response.data.detail);
    } else if (error?.message) {
      errorMessage = error.message;
    }
    return {
      total_requested: 0,
      successful_uploads: 0,
      failed_uploads: 0,
      results: [],
      success: false,
      message: errorMessage
    };
  }
}
export async function deleteSkuImage(imageId) {
  try {
    const response = await request(`/api/tools/sku-images/${imageId}`, {
      method: "DELETE"
    });
    return response;
  } catch (error) {
    console.error("deleteSkuImage error:", error);
    let errorMessage = "Failed to delete image.";
    if (error?.response?.data?.detail) {
      errorMessage = typeof error.response.data.detail === "string" ? error.response.data.detail : JSON.stringify(error.response.data.detail);
    } else if (error?.message) {
      errorMessage = error.message;
    }
    return {
      success: false,
      message: errorMessage
    };
  }
}
export async function searchSku(skuItemCode) {
  return request(`/api/tools/sku-checker`, {
    method: "GET",
    params: { sku_item_code: skuItemCode }
  });
}
export async function downloadSkuMasterData(limit = 1e3, offset = 0, search_term) {
  return request("/api/tools/stock-take/sku-master", {
    method: "GET",
    params: { limit, offset, search_term }
  });
}
export async function createStockTakeSession(sessionData) {
  return request("/api/tools/stock-take/sessions", {
    method: "POST",
    data: sessionData
  });
}
export async function getUserStockTakeSessions() {
  return request("/api/tools/stock-take/sessions", {
    method: "GET"
  });
}
export async function getUserBranchesForStockTake() {
  return request("/api/tools/stock-take/user-branches", {
    method: "GET"
  });
}
export async function saveStockTakeItems(sessionId, items) {
  return request(`/api/tools/stock-take/sessions/${sessionId}/items`, {
    method: "POST",
    data: { items }
  });
}
export async function getStockTakeItems(sessionId) {
  return request(`/api/tools/stock-take/sessions/${sessionId}/items`, {
    method: "GET"
  });
}
export async function completeStockTakeSession(sessionId) {
  return request(`/api/tools/stock-take/sessions/${sessionId}/complete`, {
    method: "POST"
  });
}
export async function exportStockTakeSession(sessionId) {
  return request(`/api/tools/stock-take/sessions/${sessionId}/export`, {
    method: "POST"
  });
}
