#!/usr/bin/env python3
"""
Debug script to test the sales summary function
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import json
from decimal import Decimal
from datetime import datetime, date

def custom_serializer(obj):
    """Custom JSON serializer for Decimal and datetime objects"""
    if isinstance(obj, Decimal):
        return float(obj)
    elif isinstance(obj, (datetime, date)):
        return obj.isoformat()
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

def test_sales_summary():
    """Test the sales summary function"""
    
    # Import after setting up the path
    from tools.sku_checker_tool import get_sku_sales_summary
    
    # Test user context
    user_context = {
        'user_id': 2,
        'role': 'user',
        'is_admin': False,
        'permitted_branches': ['MELM', 'HQFA', 'CURVE', 'SOMY']
    }
    
    print("=== Testing Sales Summary ===")
    print(f"Artno: FM9932, Mcode: 9074514")
    print(f"User context: {user_context}")
    print()
    
    try:
        result = get_sku_sales_summary('FM9932', '9074514', user_context)
        
        print("=== SALES SUMMARY RESULT ===")
        print(json.dumps(result, indent=2, default=custom_serializer))
        
        print("\n=== BRANCH DETAILS ===")
        if 'branch_details' in result:
            for i, branch in enumerate(result['branch_details']):
                print(f"Branch {i+1}:")
                print(f"  Code: {branch.get('branch_code')}")
                print(f"  Name: {branch.get('branch_name')}")
                print(f"  Total Qty: {branch.get('total_qty')}")
                print(f"  Total Sales: {branch.get('total_sales')}")
                print(f"  First Sale Date: {branch.get('first_sale_date')}")
                print(f"  Last Sale Date: {branch.get('last_sale_date')}")
                print()
        else:
            print("No branch details found!")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_sales_summary() 