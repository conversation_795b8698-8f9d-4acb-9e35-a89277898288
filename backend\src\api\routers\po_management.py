"""
PO Management API Router

This router handles Purchase Order management functionality including:
- Vendor and branch data fetching
- Excel file validation
- CSV template generation
- Multi-branch CSV file generation from Excel uploads
"""

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from fastapi.responses import StreamingResponse
from typing import Dict, Any
import logging
import io
import asyncpg

from ...auth.dependencies import get_current_active_user, get_pool
from ...models import (
    User, 
    POManagementVendorsResponse, 
    POManagementBranchesResponse,
    ExcelValidationResponse,
    POManagementUploadResponse,
    CSVTemplateResponse,
    SkuValidationRequest,
    SkuValidationResponse,
    BranchValidationRequest,
    BranchValidationResponse,
    BranchRemarkValidationResponse,
    UnifiedValidationResponse,
    PONotificationRequest,
    PONotificationResponse,
    PONotificationEmailRequest,
    PONotificationEmailResponse,
    BulkPONotificationRequest,
    BulkPONotificationResponse,
    BulkPONotificationEmailRequest,
    BulkPONotificationEmailResponse
)
from ...tools.po_management import (
    get_vendors_list,
    get_branches_list,
    validate_excel_file,
    process_excel_to_csv,
    get_csv_template_sample,
    CSV_TEMPLATE_HEADERS,
    validate_sku_codes,
    validate_branch_codes
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/po-import", tags=["PO Management"])

@router.get("/vendors", response_model=POManagementVendorsResponse)
async def get_vendors(
    current_user: User = Depends(get_current_active_user)
):
    """
    Get list of active vendors for PO management
    """
    try:
        vendors = get_vendors_list()
        return POManagementVendorsResponse(vendors=vendors)
    except Exception as e:
        logger.error(f"Error fetching vendors: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch vendors: {str(e)}")

@router.get("/branches", response_model=POManagementBranchesResponse)
async def get_branches(
    current_user: User = Depends(get_current_active_user)
):
    """
    Get list of active branches for PO management
    """
    try:
        branches = get_branches_list()
        return POManagementBranchesResponse(branches=branches)
    except Exception as e:
        logger.error(f"Error fetching branches: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch branches: {str(e)}")

@router.get("/csv-template", response_model=CSVTemplateResponse)
async def get_csv_template(
    current_user: User = Depends(get_current_active_user)
):
    """
    Get CSV template headers and sample data
    """
    try:
        sample_data = get_csv_template_sample()
        return CSVTemplateResponse(
            template_headers=CSV_TEMPLATE_HEADERS,
            sample_data=sample_data
        )
    except Exception as e:
        logger.error(f"Error getting CSV template: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get CSV template: {str(e)}")

@router.post("/validate-excel", response_model=ExcelValidationResponse)
async def validate_excel(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user)
):
    """
    Validate uploaded Excel or CSV file and return column information
    Smart detection: handles any file format regardless of extension
    """
    try:
        # Read file content
        file_content = await file.read()
        
        # Validate using the service function (smart detection)
        validation_result = validate_excel_file(file_content, file.filename)
        
        return ExcelValidationResponse(**validation_result)
        
    except Exception as e:
        logger.error(f"Error validating file: {str(e)}")
        return ExcelValidationResponse(
            valid=False,
            error=f"Error validating file: {str(e)}"
        )

@router.post("/validate-sku", response_model=SkuValidationResponse)
async def validate_sku(
    request: SkuValidationRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    Validate SKU codes from DN file against database
    Checks for:
    1. Active status (s.active = '1')
    2. Existence in database
    3. No duplicate active MCodes
    """
    try:
        validation_result = validate_sku_codes(request.sku_codes)
        return SkuValidationResponse(**validation_result)
    except Exception as e:
        logger.error(f"Error validating SKU codes: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to validate SKU codes: {str(e)}")

@router.post("/validate-branch", response_model=BranchValidationResponse)
async def validate_branch(
    request: BranchValidationRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    Validate branch codes from DN file against database
    Checks for:
    1. Active status (s.active = '1')
    2. Existence in database
    3. No duplicate active MCodes
    """
    try:
        validation_result = validate_branch_codes(request.branch_codes)
        return BranchValidationResponse(**validation_result)
    except Exception as e:
        logger.error(f"Error validating branch codes: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to validate branch codes: {str(e)}")

@router.post("/validate-branch-from-file", response_model=BranchValidationResponse)
async def validate_branch_from_file(
    file: UploadFile = File(...),
    branch_column: str = Form(...),  # Column name that contains branch codes
    current_user: User = Depends(get_current_active_user)
):
    """
    Validate branch codes by reading the full file content (not just preview)
    This endpoint reads all rows from the uploaded file to get complete branch code list
    """
    try:
        # Read file content
        file_content = await file.read()
        
        # Read full file content to extract all branch codes
        from ...tools.po_management import read_full_file_content
        
        file_data = read_full_file_content(file_content, file.filename)
        
        if not file_data.get('valid'):
            raise HTTPException(status_code=400, detail="Invalid file format")
        
        # Get available columns and find branch column
        columns = file_data.get('columns', [])
        logger.info(f"Available columns: {columns}")
        logger.info(f"Looking for branch column: '{branch_column}'")
        
        if branch_column not in columns:
            raise HTTPException(status_code=400, detail=f"Branch column '{branch_column}' not found in file")
        
        # Extract all branch codes from the specified column
        full_data = file_data.get('data', [])
        branch_codes = []
        
        for row in full_data:
            if isinstance(row, dict) and branch_column in row:
                branch_code = row[branch_column]
                if branch_code and str(branch_code).strip():
                    branch_codes.append(str(branch_code).strip())
        
        # Remove duplicates while preserving order
        unique_branch_codes = list(dict.fromkeys(branch_codes))
        
        logger.info(f"Extracted {len(unique_branch_codes)} unique branch codes from {len(full_data)} total rows")
        
        # Validate branch codes using the optimized batch function
        from ...tools.po_management import validate_branch_codes
        validation_result = validate_branch_codes(unique_branch_codes)
        
        return BranchValidationResponse(**validation_result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating branch codes from file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to validate branch codes: {str(e)}")

@router.post("/validate-sku-from-file", response_model=SkuValidationResponse)
async def validate_sku_from_file(
    file: UploadFile = File(...),
    sku_column: str = Form(...),  # Column name that contains SKU codes
    current_user: User = Depends(get_current_active_user)
):
    """
    Validate SKU codes by reading the full file content (not just preview)
    This endpoint reads all rows from the uploaded file to get complete SKU code list
    """
    try:
        # Read file content
        file_content = await file.read()
        
        # Read full file content to extract all SKU codes
        from ...tools.po_management import read_full_file_content
        
        file_data = read_full_file_content(file_content, file.filename)
        
        if not file_data.get('valid'):
            raise HTTPException(status_code=400, detail="Invalid file format")
        
        # Get available columns and find SKU column
        columns = file_data.get('columns', [])
        logger.info(f"Available columns: {columns}")
        logger.info(f"Looking for SKU column: '{sku_column}'")
        
        if sku_column not in columns:
            raise HTTPException(status_code=400, detail=f"SKU column '{sku_column}' not found in file")
        
        # Extract all SKU codes from the specified column
        full_data = file_data.get('data', [])
        sku_codes = []
        
        for row in full_data:
            if isinstance(row, dict) and sku_column in row:
                sku_code = row[sku_column]
                if sku_code and str(sku_code).strip():
                    sku_codes.append(str(sku_code).strip())
        
        # Remove duplicates while preserving order
        unique_sku_codes = list(dict.fromkeys(sku_codes))
        
        logger.info(f"Extracted {len(unique_sku_codes)} unique SKU codes from {len(full_data)} total rows")
        
        # Validate SKU codes using the optimized batch function
        from ...tools.po_management import validate_sku_codes
        validation_result = validate_sku_codes(unique_sku_codes)
        
        return SkuValidationResponse(**validation_result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating SKU codes from file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to validate SKU codes: {str(e)}")

@router.post("/validate-all-from-file", response_model=UnifiedValidationResponse)
async def validate_all_from_file(
    file: UploadFile = File(...),
    field_mapping: str = Form(...),  # JSON string of field mapping
    current_user: User = Depends(get_current_active_user)
):
    """
    Validate all three validation types (SKU, Branch, Branch Remark) in a single API call
    This improves user experience by requiring only one click instead of three
    """
    try:
        # Parse field mapping from JSON string
        import json
        try:
            field_mapping_dict = json.loads(field_mapping)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Invalid field mapping format")
        
        # Read file content once
        file_content = await file.read()
        
        # Initialize results
        validation_results = {
            "sku_validation": None,
            "branch_validation": None,
            "branch_remark_validation": None,
            "all_valid": False,
            "errors": []
        }
        
        # 1. SKU Validation
        try:
            sku_column = field_mapping_dict.get('Barcode (Mcode/Arms Code)')
            if not sku_column:
                validation_results["errors"].append("Barcode (Mcode/Arms Code) field not mapped")
            else:
                from ...tools.po_management import read_full_file_content, validate_sku_codes
                
                file_data = read_full_file_content(file_content, file.filename)
                
                if not file_data.get('valid'):
                    validation_results["errors"].append("Invalid file format for SKU validation")
                else:
                    columns = file_data.get('columns', [])
                    if sku_column not in columns:
                        validation_results["errors"].append(f"SKU column '{sku_column}' not found in file")
                    else:
                        # Extract all SKU codes
                        full_data = file_data.get('data', [])
                        sku_codes = []
                        
                        for row in full_data:
                            if isinstance(row, dict) and sku_column in row:
                                sku_code = row[sku_column]
                                if sku_code and str(sku_code).strip():
                                    sku_codes.append(str(sku_code).strip())
                        
                        # Remove duplicates while preserving order
                        unique_sku_codes = list(dict.fromkeys(sku_codes))
                        
                        # Validate SKU codes
                        sku_validation_result = validate_sku_codes(unique_sku_codes)
                        validation_results["sku_validation"] = sku_validation_result
        except Exception as e:
            logger.error(f"Error in SKU validation: {str(e)}")
            validation_results["errors"].append(f"SKU validation failed: {str(e)}")
        
        # 2. Branch Validation
        try:
            branch_column = field_mapping_dict.get('Branch Code')
            if not branch_column:
                validation_results["errors"].append("Branch Code field not mapped")
            else:
                from ...tools.po_management import read_full_file_content, validate_branch_codes
                
                file_data = read_full_file_content(file_content, file.filename)
                
                if not file_data.get('valid'):
                    validation_results["errors"].append("Invalid file format for Branch validation")
                else:
                    columns = file_data.get('columns', [])
                    if branch_column not in columns:
                        validation_results["errors"].append(f"Branch column '{branch_column}' not found in file")
                    else:
                        # Extract all branch codes
                        full_data = file_data.get('data', [])
                        branch_codes = []
                        
                        for row in full_data:
                            if isinstance(row, dict) and branch_column in row:
                                branch_code = row[branch_column]
                                if branch_code and str(branch_code).strip():
                                    branch_codes.append(str(branch_code).strip())
                        
                        # Remove duplicates while preserving order
                        unique_branch_codes = list(dict.fromkeys(branch_codes))
                        
                        # Validate branch codes
                        branch_validation_result = validate_branch_codes(unique_branch_codes)
                        validation_results["branch_validation"] = branch_validation_result
        except Exception as e:
            logger.error(f"Error in Branch validation: {str(e)}")
            validation_results["errors"].append(f"Branch validation failed: {str(e)}")
        
        # 3. Branch Remark Validation
        try:
            from ...tools.po_management import validate_branch_remark_from_file as validate_branch_remark_tool
            branch_remark_validation_result = validate_branch_remark_tool(
                file_content, 
                file.filename, 
                "Branch Remark",  # Column name after mapping
                field_mapping_dict
            )
            validation_results["branch_remark_validation"] = branch_remark_validation_result
        except Exception as e:
            logger.error(f"Error in Branch Remark validation: {str(e)}")
            validation_results["errors"].append(f"Branch Remark validation failed: {str(e)}")
        
        # Determine overall validation status
        all_valid = True
        
        # Check SKU validation
        if validation_results["sku_validation"]:
            if not validation_results["sku_validation"].get("valid", False) or validation_results["sku_validation"].get("invalid_count", 0) > 0:
                all_valid = False
        else:
            all_valid = False
        
        # Check Branch validation
        if validation_results["branch_validation"]:
            if not validation_results["branch_validation"].get("valid", False) or validation_results["branch_validation"].get("invalid_count", 0) > 0:
                all_valid = False
        else:
            all_valid = False
        
        # Check Branch Remark validation
        if validation_results["branch_remark_validation"]:
            if not validation_results["branch_remark_validation"].get("valid", False) or validation_results["branch_remark_validation"].get("empty_count", 0) > 0:
                all_valid = False
        else:
            all_valid = False
        
        validation_results["all_valid"] = all_valid
        
        return UnifiedValidationResponse(**validation_results)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in unified validation: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to perform validation: {str(e)}")

@router.post("/validate-branch-remark-from-file", response_model=BranchRemarkValidationResponse)
async def validate_branch_remark_from_file(
    file: UploadFile = File(...),
    field_mapping: str = Form(...),  # JSON string of field mapping
    current_user: User = Depends(get_current_active_user)
):
    """
    Validate Branch Remark values from uploaded file
    Checks for empty/null values which are not allowed
    """
    try:
        # Parse field mapping from JSON string
        import json
        try:
            field_mapping_dict = json.loads(field_mapping)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Invalid field mapping format")
        
        # Read file content
        file_content = await file.read()
        
        # Validate Branch Remark values
        from ...tools.po_management import validate_branch_remark_from_file as validate_branch_remark_tool
        validation_result = validate_branch_remark_tool(
            file_content, 
            file.filename, 
            "Branch Remark",  # Column name after mapping
            field_mapping_dict
        )
        
        return BranchRemarkValidationResponse(**validation_result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating Branch Remark from file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to validate Branch Remark: {str(e)}")

@router.post("/generate-csv")
async def generate_csv(
    file: UploadFile = File(...),
    vendor_code: str = Form(...),
    po_date: str = Form(...),
    delivery_date: str = Form(...),
    cancellation_date: str = Form(...),
    field_mapping: str = Form(...),  # JSON string of field mapping
    current_user: User = Depends(get_current_active_user)
):
    """
    Queue CSV generation from Excel or CSV upload using Celery background processing
    Smart detection: handles any file format regardless of extension
    """
    try:
        # Parse field mapping from JSON string
        import json
        try:
            field_mapping_dict = json.loads(field_mapping)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Invalid field mapping format")
        
        # Read file content
        file_content = await file.read()
        
        # Queue the export using the export service
        from ...services.export_service import queue_po_import_export
        
        result = queue_po_import_export(
            user_id=current_user.id,
            vendor_code=vendor_code,
            po_date=po_date,
            delivery_date=delivery_date,
            cancellation_date=cancellation_date,
            field_mapping=field_mapping_dict,
            file_content=file_content,
            filename=file.filename or "unknown.xlsx"
        )
        
        return {
            "success": True,
            "message": result["message"],
            "export_info": result["export_info"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error queuing CSV generation: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to queue CSV generation: {str(e)}")

@router.post("/test-process", response_model=POManagementUploadResponse)
async def test_process(
    file: UploadFile = File(...),
    vendor_code: str = Form(...),
    po_date: str = Form(...),
    delivery_date: str = Form(...),
    cancellation_date: str = Form(...),
    field_mapping: str = Form(...),  # JSON string of field mapping
    current_user: User = Depends(get_current_active_user)
):
    """
    Test the CSV generation process from Excel or CSV upload and return information without downloading
    Smart detection: handles any file format regardless of extension
    """
    try:
        # Parse field mapping from JSON string
        import json
        try:
            field_mapping_dict = json.loads(field_mapping)
        except json.JSONDecodeError:
            return POManagementUploadResponse(
                success=False,
                message="Invalid field mapping format"
            )
        
        # Read file content
        file_content = await file.read()
        
        # Validate file using smart detection
        validation_result = validate_excel_file(file_content, file.filename)
        if not validation_result.get("valid", False):
            return POManagementUploadResponse(
                success=False,
                message=validation_result.get("error", "Invalid file format")
            )
        
        # Process Excel to get information (without generating actual zip)
        import pandas as pd
        import io
        
        # Read Excel file with robust engine handling
        df = None
        last_error = None
        
        engines_to_try = [
            ('openpyxl', 'Modern Excel files (.xlsx)'),
            ('xlrd', 'Legacy Excel files (.xls)'),
            (None, 'Auto-detection')
        ]
        
        for engine, description in engines_to_try:
            try:
                logger.debug(f"Trying to read Excel with engine: {engine} ({description})")
                if engine:
                    df = pd.read_excel(io.BytesIO(file_content), engine=engine)
                else:
                    df = pd.read_excel(io.BytesIO(file_content))
                logger.debug(f"Successfully read Excel with engine: {engine}")
                break
            except Exception as e:
                last_error = e
                logger.debug(f"Failed to read Excel with engine {engine}: {str(e)}")
                continue
        
        if df is None:
            return POManagementUploadResponse(
                success=False,
                message=f"Could not read Excel file with any available engine. Last error: {str(last_error)}"
            )
        
        # Reverse the field mapping since frontend sends template_field -> excel_column
        # but pandas rename expects excel_column -> template_field
        reversed_mapping = {excel_col: template_field for template_field, excel_col in field_mapping_dict.items() if excel_col}
        
        logger.debug(f"Original field mapping: {field_mapping_dict}")
        logger.debug(f"Reversed field mapping for pandas: {reversed_mapping}")
        
        # Apply field mapping
        df = df.rename(columns=reversed_mapping)
        
        logger.debug(f"DataFrame columns after mapping: {list(df.columns)}")
        
        # Check if Branch Code column exists after mapping
        if 'Branch Code' not in df.columns:
            return POManagementUploadResponse(
                success=False,
                message="Branch Code column not found in field mapping. Please map a column to 'Branch Code'."
            )
        
        # Get unique branch codes
        branch_codes = df['Branch Code'].dropna().unique().tolist()
        csv_count = len(branch_codes)
        
        return POManagementUploadResponse(
            success=True,
            message=f"Ready to generate {csv_count} CSV files for branches: {', '.join(branch_codes)}",
            csv_count=csv_count,
            branch_codes=branch_codes
        )
        
    except Exception as e:
        logger.error(f"Error testing process: {str(e)}")
        return POManagementUploadResponse(
            success=False,
            message=f"Error processing file: {str(e)}"
        ) 


@router.post("/po-notification/search", response_model=PONotificationResponse)
async def search_pos_by_hq_po_id(
    request: PONotificationRequest,
    current_user: User = Depends(get_current_active_user),
    pool: asyncpg.Pool = Depends(get_pool)
):
    """
    Search for POs by HQ PO ID (Master PO No.)
    """
    try:
        from ...tools.po_notification import get_pos_by_hq_po_id
        from ...utils.db_connections import get_mysql_connection
        from ...auth.dependencies import get_user_branch_permissions, check_cost_permission
        
        # Get database connection
        db = get_mysql_connection()
        if not db:
            raise HTTPException(status_code=500, detail="Database connection failed")
        
        # Get user context for permissions
        if current_user.role == 'admin':
            permitted_branches = None  # Admin gets all access
        else:
            # Fetch user's permitted branches from PostgreSQL database
            permitted_branches = await get_user_branch_permissions(current_user.id, pool)
        
        # Check cost permission
        has_cost_permission = await check_cost_permission(current_user, pool)
        
        user_context = {
            "user_id": current_user.id,
            "role": current_user.role,
            "is_admin": current_user.role == 'admin',
            "permitted_branches": permitted_branches,
            "has_cost_permission": has_cost_permission
        }
        
        # Search for POs
        result_data = get_pos_by_hq_po_id(db, request.hq_po_id, user_context)
        
        # Close database connection
        db.close()
        
        if result_data:
            return PONotificationResponse(
                success=True,
                message=f"Found {len(result_data.get('po_data', []))} POs for HQ PO ID: {request.hq_po_id}",
                hq_po_id=request.hq_po_id,
                summary=result_data.get('summary'),
                po_data=result_data.get('po_data')
            )
        else:
            return PONotificationResponse(
                success=False,
                message=f"No POs found for HQ PO ID: {request.hq_po_id}",
                hq_po_id=request.hq_po_id,
                summary=None,
                po_data=[]
            )
            
    except Exception as e:
        logger.error(f"Error searching POs by HQ PO ID: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error searching POs: {str(e)}")


@router.post("/po-notification/send-email", response_model=PONotificationEmailResponse)
async def send_po_notification_email(
    request: PONotificationEmailRequest,
    current_user: User = Depends(get_current_active_user),
    pool: asyncpg.Pool = Depends(get_pool)
):
    """
    Send email notification for PO creation
    """
    try:
        from ...tools.po_notification import send_po_notification_email, get_pos_by_hq_po_id
        from ...auth.dependencies import check_cost_permission, get_user_branch_permissions
        from ...utils.db_connections import get_mysql_connection
        
        # Get user context for permissions
        has_cost_permission = await check_cost_permission(current_user, pool)
        permitted_branches = await get_user_branch_permissions(current_user.id, pool)
        
        user_context = {
            "user_id": current_user.id,
            "role": current_user.role,
            "is_admin": current_user.role == 'admin',
            "permitted_branches": permitted_branches,
            "has_cost_permission": has_cost_permission
        }
        
        # Get summary data by searching for the PO again
        db = get_mysql_connection()
        try:
            result_data = get_pos_by_hq_po_id(db, request.hq_po_id, user_context)
            summary = result_data.get('summary', {}) if result_data else {}
        finally:
            db.close()
        
        # Send email notification
        result = await send_po_notification_email(
            po_data=request.po_data,
            hq_po_id=request.hq_po_id,
            user_context=user_context,
            cc_emails=request.cc_emails,
            override_email=request.override_email,
            summary=summary,
            custom_subject=request.custom_subject,
            custom_message=request.custom_message
        )
        
        return PONotificationEmailResponse(
            success=result['success'],
            message=result['message'],
            po_count=result['po_count'],
            po_numbers=result['po_numbers']
        )
        
    except Exception as e:
        logger.error(f"Error sending PO notification email: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error sending email: {str(e)}")

@router.post("/po-notification/bulk-search", response_model=BulkPONotificationResponse)
async def bulk_search_po_notification(
    request: BulkPONotificationRequest,
    current_user: User = Depends(get_current_active_user),
    pool: asyncpg.Pool = Depends(get_pool)
):
    """
    Bulk search for POs by multiple HQ PO IDs
    """
    try:
        from ...tools.po_notification import get_bulk_pos_by_hq_po_ids
        from ...auth.dependencies import check_cost_permission, get_user_branch_permissions
        from ...utils.db_connections import get_mysql_connection

        # Get user context for permissions
        has_cost_permission = await check_cost_permission(current_user, pool)
        permitted_branches = await get_user_branch_permissions(current_user.id, pool)

        user_context = {
            "user_id": current_user.id,
            "role": current_user.role,
            "is_admin": current_user.role == 'admin',
            "permitted_branches": permitted_branches,
            "has_cost_permission": has_cost_permission
        }

        # Search for POs
        db = get_mysql_connection()
        try:
            result = get_bulk_pos_by_hq_po_ids(db, request.hq_po_ids, user_context)
        finally:
            db.close()

        return BulkPONotificationResponse(**result)

    except Exception as e:
        logger.error(f"Error in bulk PO notification search: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error searching POs: {str(e)}")

@router.post("/po-notification/bulk-send-email", response_model=BulkPONotificationEmailResponse)
async def bulk_send_po_notification_email(
    request: BulkPONotificationEmailRequest,
    current_user: User = Depends(get_current_active_user),
    pool: asyncpg.Pool = Depends(get_pool)
):
    """
    Send bulk email notifications for multiple Master POs
    """
    try:
        from ...tools.po_notification import send_bulk_po_notification_email
        from ...auth.dependencies import check_cost_permission, get_user_branch_permissions

        # Get user context for permissions
        has_cost_permission = await check_cost_permission(current_user, pool)
        permitted_branches = await get_user_branch_permissions(current_user.id, pool)

        user_context = {
            "user_id": current_user.id,
            "role": current_user.role,
            "is_admin": current_user.role == 'admin',
            "permitted_branches": permitted_branches,
            "has_cost_permission": has_cost_permission
        }

        # Send bulk emails
        result = await send_bulk_po_notification_email(
            hq_po_ids=request.hq_po_ids,
            bulk_results=request.bulk_results,
            user_context=user_context,
            cc_emails=request.cc_emails,
            override_email=request.override_email,
            custom_subject=request.custom_subject,
            custom_message=request.custom_message
        )

        return BulkPONotificationEmailResponse(**result)

    except Exception as e:
        logger.error(f"Error sending bulk PO notification emails: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error sending bulk emails: {str(e)}")