from pydantic import BaseModel, Field, EmailStr, validator
from typing import Optional, List, Union, Dict, Any
from datetime import datetime, date, time
from decimal import Decimal
import logging

logger = logging.getLogger(__name__)

# --- Announcement Models ---

class AnnouncementBase(BaseModel):
    title: str = Field(..., max_length=255, description="Announcement title")
    content: str = Field(..., description="Rich HTML content of the announcement")
    content_type: str = Field(default="html", description="Content type: 'text' or 'html'")
    priority: int = Field(default=1, ge=1, le=4, description="Priority level: 1=Low, 2=Medium, 3=High, 4=Critical")
    announcement_type: str = Field(default="General", description="Announcement type: Promotion, SKU, Operation, General")
    is_active: bool = Field(default=True, description="Whether the announcement is active")
    valid_from: Optional[datetime] = Field(None, description="Announcement becomes visible from this date")
    valid_to: Optional[datetime] = Field(None, description="Announcement expires after this date (NULL means no expiration)")

class AnnouncementCreate(AnnouncementBase):
    pass  # No additional fields needed for creation

class AnnouncementUpdate(BaseModel):
    title: Optional[str] = Field(None, max_length=255, description="Announcement title")
    content: Optional[str] = Field(None, description="Rich HTML content of the announcement")
    content_type: Optional[str] = Field(None, description="Content type: 'text' or 'html'")
    priority: Optional[int] = Field(None, ge=1, le=4, description="Priority level: 1=Low, 2=Medium, 3=High, 4=Critical")
    announcement_type: Optional[str] = Field(None, description="Announcement type: Promotion, SKU, Operation, General")
    is_active: Optional[bool] = Field(None, description="Whether the announcement is active")
    valid_from: Optional[datetime] = Field(None, description="Announcement becomes visible from this date")
    valid_to: Optional[datetime] = Field(None, description="Announcement expires after this date (NULL means no expiration)")

# Announcement Attachment Models (defined before Announcement to avoid forward reference)
class AnnouncementAttachment(BaseModel):
    id: int
    announcement_id: Optional[int]  # Changed from int to Optional[int] to support temporary attachments
    filename: str
    original_filename: str
    file_path: str
    file_size: int
    file_type: str
    uploaded_at: datetime
    uploaded_by: int
    uploaded_by_username: Optional[str] = None
    download_url: str  # API URL for downloading the file

    class Config:
        from_attributes = True

class AnnouncementAttachmentUpload(BaseModel):
    filename: str
    file_data: str  # Base64 encoded file data
    file_type: str

class Announcement(AnnouncementBase):
    id: int
    created_by: int
    created_at: datetime
    updated_at: datetime
    created_by_username: Optional[str] = None  # For display purposes
    attachments: Optional[List[AnnouncementAttachment]] = []  # List of file attachments
    is_read: Optional[bool] = False  # Read status for the current user
    read_at: Optional[datetime] = None  # When the user read this announcement

    class Config:
        from_attributes = True

class AnnouncementsResponse(BaseModel):
    announcements: List[Announcement]
    total_count: int
    success: bool = True
    error: Optional[str] = None

class AnnouncementAttachmentResponse(BaseModel):
    attachment: Optional[AnnouncementAttachment] = None
    success: bool = True
    message: Optional[str] = None
    error: Optional[str] = None
    file_info: Optional[dict] = None  # For temporary upload file info

class AnnouncementReadStatus(BaseModel):
    id: int
    announcement_id: int
    user_id: int
    read_at: datetime
    created_at: datetime

    class Config:
        from_attributes = True

class AnnouncementResponse(BaseModel):
    announcement: Optional[Announcement] = None
    success: bool = True
    message: Optional[str] = None
    error: Optional[str] = None

# --- Authentication & User Models ---

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

class UserBase(BaseModel):
    username: str
    email: Optional[EmailStr] = None
    role: str = 'user' # Default role
    is_active: Optional[bool] = True

class UserCreate(UserBase):
    password: str

class User(UserBase):
    id: int
    created_at: Optional[datetime] = None
    last_login: Optional[datetime] = None
    password_updated_at: Optional[datetime] = None
    branch_permissions: Optional[List[str]] = [] # Assuming this comes from DB or another source

    class Config:
        from_attributes = True

# --- Branch Model --- #

class Branch(BaseModel):
    value: str # branch_code
    label: str # branch_name

# --- Report Permission Models --- #

class ReportDefinition(BaseModel):
    report_id: str
    report_name: str
    description: Optional[str] = None
    category_name: Optional[str] = None
    is_active: bool = True

    class Config:
        from_attributes = True

class AvailableReportsResponse(BaseModel):
    reports: List[ReportDefinition]

class UserReportsResponse(BaseModel):
    reports: List[str]

class ReportListResponse(BaseModel):
    reports: List[str]

# --- Branch & Report Permission Response Models --- #

class BranchListResponse(BaseModel):
    branches: List[str]

# --- Report Models --- #

class SalesReportItem(BaseModel):
    date: str # Keep as string YYYY-MM-DD
    branch_code: str
    branch_name: str
    branch_group: Optional[str] = None
    receipt_count: int
    total_final_amount: float

# Add other models needed by your application here 

# --- Receipt Analysis Report Models --- #

class ReceiptAnalysisItem(BaseModel):
    # Match the keys returned by the service layer's processing
    Date: str
    Branch_Code: str = Field(..., alias='Branch Code')
    Branch_Name: str = Field(..., alias='Branch Name')
    Start_Receipt: str = Field(..., alias='Start Receipt')
    End_Receipt: str = Field(..., alias='End Receipt')
    Receipt_Count: int = Field(..., alias='Receipt Count')
    Total_Amount: float = Field(..., alias='Total Amount')

    class Config:
        populate_by_name = True # Allow using alias for input fields

class ReportSummary(BaseModel):
    total_records: int
    total_receipts: int
    total_amount: float
    avg_receipt_amount: float
    branch_count: int
    date_range: str

class FetchReceiptAnalysisResponse(BaseModel):
    data: List[ReceiptAnalysisItem]
    summary: ReportSummary
    effective_branch_filter: List[str]
    error: Optional[str] = None
    success: bool = True # Default to True, set to False on error 

# --- Sales By SKU Report Models --- #

# Define model representing one row of the salesbysku table/report
# Use Optional for fields that might be null, adjust types based on schema/usage
class SalesBySkuItem(BaseModel):
    date: Optional[datetime] = None # Or str if always formatted
    branch_code: Optional[str] = None
    branch_name: Optional[str] = None
    branch_group: Optional[str] = None
    pos_time: Optional[str] = None # Or time object
    line_item: Optional[int] = None
    receipt_ref_no: Optional[str] = None
    receipt_no: Optional[str] = None
    armscode: Optional[str] = None
    art_no: Optional[str] = None
    mcode: Optional[str] = None
    art_no_description: Optional[str] = None
    sku_description_sz_cl: Optional[str] = None
    brand: Optional[str] = None
    department: Optional[str] = None
    category: Optional[str] = None
    type_of_silhouette: Optional[str] = None
    silhouette: Optional[str] = None
    gender: Optional[str] = None
    age: Optional[str] = None
    color: Optional[str] = None
    size: Optional[str] = None
    season_code: Optional[str] = None
    vendor: Optional[str] = None
    qty_sold: Optional[float] = None # Use float for flexibility post-pandas
    rsp: Optional[float] = None
    cost_price: Optional[Union[float, str]] = None  # Can be float or permission string
    hq_cost: Optional[Union[float, str]] = None  # Can be float or permission string
    rsp_discount: Optional[str] = None # String from schema
    unit_price_after_rsp_disc: Optional[Union[float, str]] = None  # Can be float or permission string
    after_disc_rsp: Optional[Union[float, str]] = None  # Can be float or permission string
    discount_1_promo_disc: Optional[float] = None
    discount_2_mix_match: Optional[float] = None
    net_selling_price: Optional[float] = None
    gp_amount: Optional[Union[float, str]] = None  # Can be float or permission string
    gp_percentage: Optional[Union[float, str]] = None  # Can be float or permission string
    discount_type: Optional[str] = None
    staff_id: Optional[str] = None
    staff_name: Optional[str] = None
    promotion_name: Optional[str] = None

    class Config:
        from_attributes = True

# --- Notification Models ---

class NotificationResponse(BaseModel):
    """Individual notification item response"""
    id: str
    type: str  # 'failure', 'warning', 'info'
    title: str
    message: str
    mall_code: str
    mall_name: str
    created_at: str  # ISO datetime string
    details: Optional[Dict[str, Any]] = None

class NotificationCountResponse(BaseModel):
    """Notification count response"""
    failure: int = 0
    warning: int = 0
    info: int = 0
    total: int = 0

class NotificationListResponse(BaseModel):
    """List of notifications response"""
    notifications: List[NotificationResponse]
    count: NotificationCountResponse
    success: bool = True
    error: Optional[str] = None

class NotificationDismissRequest(BaseModel):
    """Request to dismiss a notification"""
    notification_id: str

class NotificationDismissResponse(BaseModel):
    """Response for notification dismissal"""
    success: bool = True
    message: str
    error: Optional[str] = None# Allow creating from dict/ORM

# Define model for the summary statistics
class SalesBySkuSummary(BaseModel):
    total_records: int
    total_qty_sold: int
    total_sales: Optional[float] = None  # Made optional for cost filtering
    total_gp_amount: Optional[Union[float, str]] = None  # Can be float or permission string
    avg_gp_percentage: Optional[Union[float, str]] = None  # Can be float or permission string
    brands_count: int
    transaction_count: int
    date_range: str

# Define the overall response structure for the API endpoint
class FetchSalesBySkuResponse(BaseModel):
    data: List[SalesBySkuItem]
    summary: SalesBySkuSummary
    columns: List[str] # Include column names for dynamic frontend rendering
    total_records: Optional[int] = None  # Total records available in database
    displayed_records: Optional[int] = None  # Number of records actually displayed
    is_limited: Optional[bool] = False  # Whether results are limited for preview
    limit_message: Optional[str] = None  # Message about record limitation
    error: Optional[str] = None
    success: bool = True

# --- Endpoint for Latest SKU Date --- #
class LatestDateResponse(BaseModel):
    latest_date: Optional[str] = None
    message: Optional[str] = None
    success: bool = True

# --- PO Details Report Models --- #

# Represents one row/item in the PO Details report
class PODetailItem(BaseModel):
    po_date: Optional[str] = None # Assuming date as string YYYY-MM-DD
    po_branch_code: Optional[str] = None # Corrected
    po_branch_name: Optional[str] = None # Corrected
    po_no: Optional[str] = None # Corrected
    vendor_code: Optional[str] = None # Corrected
    vendor_name: Optional[str] = None # Corrected
    armscode: Optional[str] = None # Corrected
    art_no_description: Optional[str] = None # Corrected
    po_ordered_pcs: Optional[float] = None # Corrected (Using float for flexibility)
    po_cost_price: Optional[float] = None # Corrected
    total_po_netamt: Optional[float] = None # Corrected
    # Add other relevant fields from podetails table as needed

    class Config:
        from_attributes = True

# Represents the summary statistics for the PO Details report
class PODetailSummary(BaseModel):
    total_records: int
    total_quantity_ordered: Optional[Union[float, str]] = None  # Can be float or permission string (cost related)
    total_po_cost: Optional[Union[float, str]] = None  # Can be float or permission string (cost field)
    unique_pos: int
    unique_suppliers: int
    date_range: str

    class Config:
        from_attributes = True

# --- NEW: PO Summary Item Model --- #
class POSummaryItem(BaseModel):
    # Match the columns selected and aliased in the summary query
    po_date: Optional[str] = None 
    po_branch_code: Optional[str] = None
    po_branch_name: Optional[str] = None
    hq_master_po: Optional[str] = None
    po_no: Optional[str] = None
    po_status: Optional[str] = None
    po_active: Optional[str] = None
    po_approved: Optional[str] = None
    po_delivered: Optional[str] = None
    delivery_date: Optional[str] = None
    cancellation_date: Optional[str] = None # Renamed from cancel_date to match query
    po_remark: Optional[str] = None
    po_remark2: Optional[str] = None
    vendor_code: Optional[str] = None
    vendor_name: Optional[str] = None
    total_ordered_ctn: Optional[Decimal] = None # Changed from int to Decimal
    total_ordered_pcs: Optional[Decimal] = None # Changed from int to Decimal
    total_net_amount: Optional[Union[Decimal, str]] = None  # Can be Decimal or permission string (cost field)
    total_rsp: Optional[Decimal] = None
    total_gp_rm: Optional[Union[Decimal, str]] = None  # Can be Decimal or permission string (cost field)
    gross_profit_percentage: Optional[Union[Decimal, str]] = None  # Can be Decimal or permission string (cost field)
    gr_status: Optional[str] = None
    grn_rcv_date: Optional[str] = None
    grn_no: Optional[str] = None
    total_grn_doc_amount: Optional[Decimal] = None
    total_grn_ctn: Optional[Decimal] = None
    total_grn_pcs: Optional[Decimal] = None
    total_grn_cost: Optional[Union[Decimal, str]] = None  # Can be Decimal or permission string (cost field)
    total_variance_ctn: Optional[Decimal] = None
    total_variance_pcs: Optional[Decimal] = None
    total_variance_cost: Optional[Union[Decimal, str]] = None  # Can be Decimal or permission string (cost field)
    grn_approve: Optional[str] = None
    supplier_po: Optional[str] = None
    grn_do_no: Optional[str] = None
    supplier_invoice: Optional[str] = None
    other: Optional[str] = None
    total_sku_count: Optional[int] = None # Added missing field

    class Config:
        from_attributes = True

# --- Updated API Response to handle Union type --- #
# Option 1: Use Union (More specific) 
class FetchPODetailsResponse(BaseModel):
    data: Union[List[PODetailItem], List[POSummaryItem]] # Can be list of details OR summary
    summary: PODetailSummary # Summary might be less relevant for Summary view, but keep for now
    columns: List[str] # Dynamic columns from the query result
    effective_branch_filter: Optional[List[str]] = None
    error: Optional[str] = None
    success: bool = True
    # Add limitation fields like Sales by SKU
    total_records: Optional[int] = None  # Total records available in database
    displayed_records: Optional[int] = None  # Number of records actually displayed
    is_limited: Optional[bool] = False  # Whether results are limited for preview
    limit_message: Optional[str] = None  # Message about record limitation

# --- Export Request/Response Models (Ensure these are defined) --- #

class ExportRequestPayload(BaseModel):
    """Generic payload for the POST /api/exports endpoint."""
    report_name: str
    filters: Dict[str, Any] # Filters can vary, use a flexible dict

class ExportResponse(BaseModel):
    """Generic response for export queuing endpoints."""
    success: bool = True
    message: str
    export_id: Optional[int] = None
    filename: Optional[str] = None # Keep optional fields from previous definition
    warning: Optional[str] = None
    error: Optional[str] = None

# --- Specific Report Export Request Models (Keep if used by specific endpoints) --- #

class SkuExportRequestPayload(BaseModel):
    startDate: Optional[str] = None
    endDate: Optional[str] = None
    branchCode: Optional[List[str]] = None
    brand: Optional[str] = None
    department: Optional[str] = None

# Updated PODetailsExportRequest to match the comprehensive definition from main.py
class PODetailsExportRequest(BaseModel):
    # startDate: date
    # endDate: date
    startDate: str # Temporarily accept string
    endDate: str   # Temporarily accept string
    branchCodes: Optional[str] = None # Plural, comma-separated string
    viewType: str = "details"        # Added field, with default
    poApproved: Optional[str] = None # Added field
    vendorName: Optional[str] = None # Added field
    grStatus: Optional[str] = None   # Added field
    grnApprove: Optional[str] = None # Added field

# Add other models as needed 

# Models for Delivery Order Details Report

class DOBase(BaseModel):
    do_date: Optional[date] = None
    do_no: Optional[str] = None
    po_no: Optional[str] = None
    do_type: Optional[str] = None
    do_price_type: Optional[str] = None
    do_status: Optional[str] = None
    do_approved: Optional[str] = None
    remark: Optional[str] = None
    from_branch_code: Optional[str] = None
    from_branch: Optional[str] = None
    to_branch_code: Optional[str] = None
    to_branch: Optional[str] = None
    checkout_status: Optional[str] = None
    checkout_remark: Optional[str] = None
    gr_status: Optional[str] = None
    grn_rcv_date: Optional[date] = None
    grr_id: Optional[int] = None # Changed from Optional[str] to Optional[int]
    grn_no: Optional[str] = None
    grn_approve: Optional[str] = None
    active: Optional[str] = None # From summary view in sample

class DODetailItem(DOBase):
    branch_id: Optional[int] = None # Changed from Optional[str] to Optional[int]
    armscode: Optional[str] = None
    mcode: Optional[str] = None
    artno: Optional[str] = None
    brand: Optional[str] = None
    art_no_description: Optional[str] = None
    department: Optional[str] = None
    category: Optional[str] = None
    type_of_silhouette: Optional[str] = None
    silhouette: Optional[str] = None
    gender: Optional[str] = None
    age: Optional[str] = None
    color: Optional[str] = None
    size: Optional[str] = Field(None, alias="size") # Assuming 'size' is the column name
    season_code: Optional[str] = None
    sku_description_sz_cl: Optional[str] = None
    ctn: Optional[Decimal] = None # Assuming ctn can be decimal
    do_qty: Optional[Decimal] = None
    do_item_cost: Optional[Union[Decimal, str]] = None  # Can be Decimal or permission string
    cost_price: Optional[Union[Decimal, str]] = None  # Can be Decimal or permission string
    rsp_price: Optional[Decimal] = None
    rsp_discount: Optional[str] = None  # Keep as string to preserve % symbols
    selling_price: Optional[Union[Decimal, str]] = None  # Can be Decimal or permission string
    vendor_code: Optional[str] = None
    vendor: Optional[str] = None
    created_at: Optional[datetime] = None

    class Config:
        orm_mode = True
        allow_population_by_field_name = True


class DODetailSummaryItem(DOBase): # Inherits common fields from DOBase
    ctn: Optional[Decimal] = None # For SUM(ctn)
    total_qty: Optional[Decimal] = None
    total_cost: Optional[Union[Decimal, str]] = None  # Can be Decimal or permission string
    # New GRN-related fields for summary view
    grn_no: Optional[str] = None
    grn_approve: Optional[str] = None
    grn_pcs: Optional[Decimal] = None
    total_grn_cost: Optional[Union[Decimal, str]] = None  # Can be Decimal or permission string (cost field)
    variance_qty_pcs: Optional[Decimal] = None
    variance_cost: Optional[Union[Decimal, str]] = None  # Can be Decimal or permission string (cost field)
    grn_doc_details: Optional[str] = None

    class Config:
        orm_mode = True

class DOFilterOption(BaseModel):
    id: str
    name: str

class DOReportFilterOptionsResponse(BaseModel):
    do_statuses: List[DOFilterOption] = []
    do_approved_options: List[DOFilterOption] = []
    gr_statuses: List[DOFilterOption] = []
    checkout_statuses: List[DOFilterOption] = []

class DODetailsSummaryStats(BaseModel):
    grand_total_ctn: float = 0.0
    grand_total_do_qty: float = 0.0
    grand_total_do_cost: Optional[Union[float, str]] = None  # Can be float or permission string
    grand_total_price: float = 0.0
    total_unique_dos: int = 0
    total_unique_items: int = 0
    # New GRN-related summary fields
    grand_total_grn_pcs: float = 0.0
    grand_total_grn_cost: Optional[Union[float, str]] = None  # Can be float or permission string (cost field)
    grand_total_variance_qty_pcs: float = 0.0
    grand_total_variance_cost: Optional[Union[float, str]] = None  # Can be float or permission string (cost field)

class FetchDODetailsResponse(BaseModel):
    data: Union[List[DODetailItem], List[DODetailSummaryItem]]
    total_count: int # Renamed from total to total_count
    summary_stats: Optional[DODetailsSummaryStats] = None # For summary view
    success: bool = True # Added success field with default True
    # Add limitation fields like Sales by SKU and PO Details
    total_records: Optional[int] = None  # Total records available in database
    displayed_records: Optional[int] = None  # Number of records actually displayed
    is_limited: Optional[bool] = False  # Whether results are limited for preview
    limit_message: Optional[str] = None  # Message about record limitation

class DOLatestDateResponse(BaseModel):
    latest_date: Optional[date] = None

# Ensure all Pydantic models that might be returned by API endpoints are defined.
# Make sure existing model imports and definitions are not disturbed. 

class UserUpdate(BaseModel):
    email: Optional[str] = None
    full_name: Optional[str] = None
    role: Optional[str] = None
    is_active: Optional[bool] = None
    password: Optional[str] = None

class UserResponse(UserBase):
    id: int
    role: str
    is_active: bool
    # Ensure this is not unintentionally orm_mode = True if not an ORM model
    # class Config:
    #     orm_mode = True # Usually for SQLAlchemy models read directly

class BranchFilter(BaseModel):
    id: int
    name: str
    branches: List[str]

class SkuBrand(BaseModel):
    id: str
    name: str

class SkuDepartment(BaseModel):
    id: str
    name: str

class OutletSalesData(BaseModel):
    summary: Dict[str, Union[float, int]]
    details: List[SalesReportItem]
    categories: List[str] # For chart categories (branch names)
    series_data: Dict[str, List[Union[float, int]]] # For chart series data

class FetchOutletSalesResponse(BaseModel):
    data: OutletSalesData
    latest_data_date: Optional[date] = None

class ExportRequest(BaseModel):
    filters: Dict[str, Any]
    report_name: Optional[str] = None # Make optional if not always needed or derived elsewhere
    user_context: Dict[str, Any] # To pass user permissions for data scoping

class ExportHistoryItem(BaseModel):
    id: int
    report_name: str
    file_name: Optional[str] = None
    status: str
    created_at: datetime
    created_by_username: str # Added username
    file_path: Optional[str] = None # Added for download link construction

class FetchExportHistoryResponse(BaseModel):
    data: List[ExportHistoryItem]
    total: int

# The following are duplicate or conflicting definitions and will be removed.
# Correct definitions appear earlier in the file.

# class FetchSalesBySkuResponse(BaseModel):
#     data: List[SalesBySkuItem]
#     total: int
#     latest_data_date: Optional[date] = None

# class LatestDateResponse(BaseModel):
#     latest_date: Optional[date]

# class PODetailItem(BaseModel):
#     po_no: str
#     po_date: date
#     # ... (other fields from the duplicate PODetailItem)

# class PODetailSummary(BaseModel):
#     total_pos: int
#     # ... (other fields from the duplicate PODetailSummary)

# class FetchPODetailsResponse(BaseModel):
#     data: List[PODetailItem]
#     # ... (other fields from the duplicate FetchPODetailsResponse)

# class DODetailItem(BaseModel):
#     do_no: str
#     do_date: date
#     # ... (other fields from the duplicate DODetailItem)

# class DODetailSummary(BaseModel):
#     total_dos: int
#     # ... (other fields from the duplicate DODetailSummary)

# class FetchDODetailsResponse(BaseModel):
#     data: List[DODetailItem]
#     # ... (other fields from the duplicate FetchDODetailsResponse)


# --- SKU Image Models ---
class SkuImageItem(BaseModel):
    id: Optional[int] = None
    artno: str
    filename: str  # Image filename (e.g., "DH3160-001.jpg")
    local_path: str  # Relative path (e.g., "/sku_images/DH3160-001.jpg")
    original_url: Optional[str] = None
    created_at: Optional[datetime] = None
    image_url: str  # API URL for accessing the image

    class Config:
        from_attributes = True

# --- SKU Checker Models (These are unique and should remain) ---
class SkuDetailItem(BaseModel):
    sku_item_code: str
    art_no: Optional[str] = Field(None, alias='Art_No')
    mcode: Optional[str] = Field(None, alias='MCode')
    art_no_description: Optional[str] = Field(None, alias='ART_NO DESCRIPTION')
    brand: Optional[str] = Field(None, alias='Brand')
    department: Optional[str] = Field(None, alias='Department')
    category: Optional[str] = Field(None, alias='Category')
    type_of_silhouette: Optional[str] = Field(None, alias='Type_of_Silhouette')
    silhouette: Optional[str] = Field(None, alias='Silhouette')
    gender: Optional[str] = Field(None, alias='Gender')
    age: Optional[str] = Field(None, alias='Age')
    color: Optional[str] = Field(None, alias='Color')
    size: Optional[str] = Field(None, alias='Size')
    vendor: Optional[str] = Field(None, alias='Vendor')
    season_code: Optional[str] = Field(None, alias='Season_Code')
    cost_price: Optional[Union[float, str]] = Field(None, alias='Cost_Price')  # Can be float or permission string
    hq_cost: Optional[Union[float, str]] = Field(None, alias='HQ_Cost')  # Can be float or permission string
    rsp: Optional[float] = Field(None, alias='RSP')
    rsp_discount: Optional[str] = Field(None, alias='RSP_Discount')
    net_selling_price: Optional[float] = Field(None, alias='Net_Selling_Price')
    active: str = Field(..., alias='Active') # 'Active', 'Inactive', or 'Error'
    lastupdate: Optional[datetime] = None
    is_parent: Optional[str] = None
    # Image-related fields
    images: Optional[List[SkuImageItem]] = []  # List of associated images
    image_count: Optional[int] = 0  # Number of images available

    class Config:
        orm_mode = True
        populate_by_name = True # Allows using aliases

class SkuImageUploadRequest(BaseModel):
    artno: str
    image_data: str  # Base64 encoded image data
    filename: Optional[str] = None
    remove_background: Optional[bool] = True  # Enable background removal by default

class BulkImageUploadItem(BaseModel):
    artno: str
    image_data: str  # Base64 encoded image data
    filename: Optional[str] = None
    remove_background: Optional[bool] = True  # Enable background removal by default

class BulkImageUploadRequest(BaseModel):
    images: List[BulkImageUploadItem] = Field(..., description="List of images to upload")

class BulkImageUploadResult(BaseModel):
    artno: str
    filename: Optional[str] = None
    success: bool
    message: str
    image_id: Optional[int] = None
    local_path: Optional[str] = None
    error: Optional[str] = None

class BulkImageUploadResponse(BaseModel):
    total_requested: int
    successful_uploads: int
    failed_uploads: int
    results: List[BulkImageUploadResult]
    success: bool = True
    message: str

class BulkImageUploadWithTemplateItem(BaseModel):
    artno: str
    image_data: str  # Base64 encoded image data
    filename: Optional[str] = None
    remove_background: Optional[bool] = True  # Enable background removal by default
    apply_template: Optional[bool] = True  # Apply brand template by default
    product_scale: Optional[float] = Field(default=0.95, ge=0.1, le=1.0, description="Product scale factor (0.1-1.0)")
    manual_template: Optional[str] = Field(default=None, description="Manually selected template (overrides auto-detection)")

class BulkImageUploadWithTemplateRequest(BaseModel):
    images: List[BulkImageUploadWithTemplateItem] = Field(..., description="List of images to upload with template processing")

class BulkImageDeleteRequest(BaseModel):
    image_ids: List[int]

class BulkImageDeleteResponse(BaseModel):
    success: bool
    message: str
    success_count: int
    error_count: int
    errors: Optional[List[str]] = None

class SkuImageDeleteRequest(BaseModel):
    image_id: int

class GoogleImageSearchRequest(BaseModel):
    artno: str
    search_query: Optional[str] = None  # Optional custom search query
    num_results: Optional[int] = Field(default=10, le=20)  # Limit to 20 results max

class GoogleImageSearchResult(BaseModel):
    title: str
    link: str
    thumbnail: str
    context_link: str
    width: Optional[int] = None
    height: Optional[int] = None

class GoogleImageSearchResponse(BaseModel):
    results: List[GoogleImageSearchResult]
    search_query: str
    total_results: int
    success: bool = True
    error: Optional[str] = None

class SkuImageSaveRequest(BaseModel):
    artno: str
    image_url: str
    image_title: Optional[str] = None
    remove_background: Optional[bool] = True  # Enable background removal by default

class SkuImageSaveWithTemplateRequest(BaseModel):
    artno: str
    image_url: str
    image_title: Optional[str] = None
    remove_background: Optional[bool] = True
    save_mode: str = Field(..., description="Save mode: 'with_template', 'without_template', or 'both'")
    product_scale: Optional[float] = Field(default=0.95, ge=0.1, le=1.0, description="Product scale factor (0.1-1.0)")
    manual_template: Optional[str] = Field(default=None, description="Manually selected template (overrides auto-detection)")

class SkuImagePreviewRequest(BaseModel):
    artno: str
    image_url: str
    product_scale: Optional[float] = Field(default=0.95, ge=0.1, le=1.0, description="Product scale factor (0.1-1.0)")
    remove_background: Optional[bool] = True
    manual_template: Optional[str] = Field(default=None, description="Manually selected template (overrides auto-detection)")

class SkuImagePreviewResponse(BaseModel):
    success: bool = True
    preview_url: str  # Base64 data URL for preview
    brand: Optional[str] = None
    has_template: bool = False
    product_scale: float = 0.95
    message: Optional[str] = None
    error: Optional[str] = None

class SkuImageResponse(BaseModel):
    success: bool = True
    message: str
    image_id: Optional[int] = None
    local_path: Optional[str] = None
    filename: Optional[str] = None
    error: Optional[str] = None

class SkuImagesListResponse(BaseModel):
    images: List[SkuImageItem]
    total_count: int
    artno: str
    success: bool = True
    error: Optional[str] = None

class FetchSkuDetailsResponse(BaseModel):
    data: List[SkuDetailItem]
    current_batch_count: int # Number of items in this specific batch/page
    grand_total: int         # Total number of items matching criteria in the DB
    success: bool = True
    error: Optional[str] = None
    # New fields for handling large result sets
    is_result_limited: Optional[bool] = False  # Whether results were limited due to size
    result_limit_message: Optional[str] = None  # Message about result limitation
    export_recommended: Optional[bool] = False  # Whether export is recommended for full results

class SkuCheckerExportRequest(BaseModel):
    sku_item_code: Optional[str] = None
    artno: Optional[str] = None
    mcode: Optional[str] = None
    description: Optional[str] = None
    search_scope: Optional[str] = "parent"
    use_wildcard: Optional[bool] = False

class ActivityLogDB(BaseModel): # Renamed from ActivityLogIn to avoid conflict if used for DB operations
    id: Optional[int] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    username: Optional[str] = None
    action: str
    details: Optional[Dict[str, Any]] = None

    class Config:
        orm_mode = True 

# --- Price Checker Models --- #

class PriceCheckerSearchRequest(BaseModel):
    mcode: str # Changed from search_term to mcode
    branch_code: Optional[str] = None # Added optional branch_code

# New enhanced search request for both MCode and ArtNo
class PriceCheckerEnhancedSearchRequest(BaseModel):
    search_type: str = Field(..., description="Search type: 'mcode' or 'artno'")
    search_value: str = Field(..., description="Value(s) to search for (MCode or ArtNo), multiple values separated by newlines")
    branch_code: Optional[str] = None # Added optional branch_code

class PriceInfoItem(BaseModel):
    # Fields from sku_items and related tables (common details)
    armscode: Optional[str] = None
    mcode: Optional[str] = None # This is the search key, but also good to return
    artno: Optional[str] = None
    sku_description: Optional[str] = None
    brand: Optional[str] = None
    department: Optional[str] = None
    category: Optional[str] = None
    color: Optional[str] = None
    size: Optional[str] = None # Changed from size_val with alias
    rsp_price: Optional[float] = None
    rsp_discount: Optional[str] = None # Assuming this can be textual, e.g., "10%"
    selling_price: Optional[float] = None
    last_update: Optional[str] = None # From si.lastupdate, should be formatted to string

    # Conditional fields if branch_code is provided
    branch_code_result: Optional[str] = None
    branch_name: Optional[str] = None
    branch_price: Optional[float] = None
    branch_price_update: Optional[str] = None # From sip.last_update, format to string

    class Config:
        from_attributes = True 
        # populate_by_name = True # No longer needed for size alias

class PriceCheckerResponse(BaseModel):
    data: Optional[PriceInfoItem] = None
    success: bool = True
    message: Optional[str] = None 

# New enhanced response for multiple results (ArtNo search)
class PriceCheckerEnhancedResponse(BaseModel):
    data: Optional[List[PriceInfoItem]] = None
    search_type: str
    search_value: str
    total_variants: int = 0
    success: bool = True
    message: Optional[str] = None

# Branch price information model
class BranchPriceInfo(BaseModel):
    branch_code: str
    branch_name: str
    branch_price: Optional[float] = None
    branch_price_update: Optional[str] = None

# Enhanced response with branch prices for MCode searches
class PriceCheckerWithBranchPricesResponse(BaseModel):
    data: Optional[List[PriceInfoItem]] = None
    branch_prices: Optional[List[BranchPriceInfo]] = None
    search_type: str
    search_value: str
    total_variants: int = 0
    success: bool = True
    message: Optional[str] = None

# Models for DO Checker Tool

class DOCheckerSkuDetail(BaseModel):
    """Individual SKU detail within a color group"""
    mcode: Optional[str] = None
    size: Optional[str] = None
    qty: Optional[float] = None
    armscode: Optional[str] = None
    sku_description_sz_cl: Optional[str] = None
    do_item_cost: Optional[Union[float, str]] = None  # Can be float or permission string
    cost_price: Optional[Union[float, str]] = None  # Can be float or permission string
    rsp_price: Optional[float] = None
    selling_price: Optional[float] = None

    class Config:
        from_attributes = True

class DOCheckerArtnoDetails(BaseModel):
    art_no_description: Optional[str] = None # Changed from sku_description_sz_cl
    brand: Optional[str] = None
    department: Optional[str] = None
    category: Optional[str] = None
    gender: Optional[str] = None
    total_artno_qty: Optional[float] = None # Added total quantity for the article

class DOCheckerSkuPivotedData(BaseModel):
    unique_sizes: List[str]
    color_data: List[Dict[str, Any]] # Each dict: {"color": "Black", "sku_details": [...], "size1": qty1, ...}
    artno_details: DOCheckerArtnoDetails

class DOCheckerSummaryItem(BaseModel):
    # Basic DO Information
    do_date: Optional[date] = None
    do_no: Optional[str] = None
    from_branch_code: Optional[str] = None
    from_branch: Optional[str] = None
    to_branch_code: Optional[str] = None
    to_branch: Optional[str] = None
    total_do_qty: Optional[float] = None
    total_ctn: Optional[float] = None
    total_do_item_cost: Optional[Union[float, str]] = None  # Can be float or permission string
    remark: Optional[str] = None
    
    # Status Information
    do_status: Optional[str] = None
    do_approved: Optional[str] = None
    checkout_status: Optional[str] = None
    checkout_remark: Optional[str] = None
    
    # GR Information  
    gr_status: Optional[str] = None
    gr_date: Optional[date] = None # from MAX(grn_rcv_date)
    grn_no: Optional[str] = None # GR No - new field
    grn_approve: Optional[str] = None # GR Approve - new field
    total_variance_cost: Optional[Union[float, str]] = None # Can be float or permission string
    total_variance_qty: Optional[float] = None # Total Variance Qty - new field

    @validator("gr_date", "do_date", pre=True)
    def ensure_date_type(cls, value):
        if isinstance(value, datetime):
            return value.date()
        if isinstance(value, str):
            try:
                return datetime.strptime(value, "%Y-%m-%d").date()
            except ValueError:
                return value 
        return value

class DOCheckerResponse(BaseModel):
    summary: Optional[DOCheckerSummaryItem] = None
    pivoted_sku_data: Optional[Dict[str, DOCheckerSkuPivotedData]] = None # Keyed by artno
    message: Optional[str] = None 

# --- Sync Tools Models --- #

class SyncToolDefinition(BaseModel):
    tool_id: str
    tool_name: str
    description: Optional[str] = None
    category: Optional[str] = None
    is_active: bool = True

class SyncExecutionRecord(BaseModel):
    id: Optional[int] = None
    tool_id: str
    status: str  # 'running', 'completed', 'failed', 'cancelled'
    started_at: datetime
    completed_at: Optional[datetime] = None
    executed_by: str
    records_processed: Optional[int] = None
    error_message: Optional[str] = None
    execution_details: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True

class SyncToolsResponse(BaseModel):
    tools: List[SyncToolDefinition]

class SyncExecutionResponse(BaseModel):
    success: bool = True
    message: str
    execution_id: Optional[int] = None
    status: str

class SyncHistoryResponse(BaseModel):
    data: List[SyncExecutionRecord]
    total: int

class SalesSyncRequest(BaseModel):
    year: Optional[int] = None
    month: Optional[int] = None
    full_sync: bool = False

    @validator('month')
    def validate_month(cls, v):
        if v is not None and (v < 1 or v > 12):
            raise ValueError('Month must be between 1 and 12')
        return v

    @validator('year')
    def validate_year(cls, v):
        if v is not None and (v < 2020 or v > 2030):
            raise ValueError('Year must be between 2020 and 2030')
        return v

class DOSyncRequest(BaseModel):
    year: Optional[int] = None
    month: Optional[int] = None
    full_sync: bool = False

    @validator('month')
    def validate_month(cls, v):
        if v is not None and (v < 1 or v > 12):
            raise ValueError('Month must be between 1 and 12')
        return v

    @validator('year')
    def validate_year(cls, v):
        if v is not None and (v < 2020 or v > 2030):
            raise ValueError('Year must be between 2020 and 2030')
        return v

class GRNSyncRequest(BaseModel):
    year: Optional[int] = None
    month: Optional[int] = None
    full_sync: bool = False

    @validator('month')
    def validate_month(cls, v):
        if v is not None and (v < 1 or v > 12):
            raise ValueError('Month must be between 1 and 12')
        return v

    @validator('year')
    def validate_year(cls, v):
        if v is not None and (v < 2020 or v > 2030):
            raise ValueError('Year must be between 2020 and 2030')
        return v

class POSyncRequest(BaseModel):
    year: Optional[int] = None
    month: Optional[int] = None
    full_sync: bool = False

    @validator('month')
    def validate_month(cls, v):
        if v is not None and (v < 1 or v > 12):
            raise ValueError('Month must be between 1 and 12')
        return v

    @validator('year')
    def validate_year(cls, v):
        if v is not None and (v < 2020 or v > 2030):
            raise ValueError('Year must be between 2020 and 2030')
        return v

class StockBalanceSyncRequest(BaseModel):
    report_date: str  # Required field in YYYY-MM-DD, MM-YYYY, or YYYY-MM format
    branch_codes: Optional[List[str]] = None  # Optional list of branch codes
    full_sync: bool = False
    from_date: Optional[str] = None  # Optional start date filter
    to_date: Optional[str] = None    # Optional end date filter
    stock_balance_table: Optional[str] = None  # Optional specific stock balance table name

    @validator('report_date')
    def validate_report_date(cls, v):
        if not v:
            raise ValueError('Report date is required')
        return v 

# --- Stock Inventory Report Models --- #

class StockInventoryItem(BaseModel):
    branch_code: Optional[str] = None
    branch_name: Optional[str] = None
    armscode: Optional[str] = None
    mcode: Optional[str] = None
    artno: Optional[str] = None
    art_no_description: Optional[str] = None
    sku_description_sz_cl: Optional[str] = None
    brand: Optional[str] = None
    department: Optional[str] = None
    category: Optional[str] = None
    type_of_silhouette: Optional[str] = None
    silhouette: Optional[str] = None
    gender: Optional[str] = None
    age: Optional[str] = None
    color: Optional[str] = None
    size: Optional[str] = None
    season_code: Optional[str] = None
    qty: Optional[Decimal] = None
    unverified_qty: Optional[Decimal] = None
    do_qty_nocheckout: Optional[Decimal] = None
    do_qty_pending_recv: Optional[Decimal] = None
    total_unverified_qty: Optional[Decimal] = None
    cost_price: Optional[Union[Decimal, str]] = None  # Can be Decimal or permission string
    rsp_price: Optional[Decimal] = None
    rsp_discount: Optional[Decimal] = None
    selling_price: Optional[Decimal] = None
    stock_value_at_cost: Optional[Union[Decimal, str]] = None  # Can be Decimal or permission string
    stock_value_at_cost_unverified_qty: Optional[Union[Decimal, str]] = None  # Can be Decimal or permission string
    stock_value_at_rsp: Optional[Decimal] = None
    lastupdate: Optional[datetime] = None
    vendor_code: Optional[str] = None
    vendor: Optional[str] = None
    report_date: Optional[date] = None
    sku_active: Optional[str] = None

    class Config:
        from_attributes = True

class StockInventorySummaryStats(BaseModel):
    total_qty: float = 0.0
    total_unverified_qty: float = 0.0
    grand_total_unverified_qty: float = 0.0
    total_stock_value_cost: float = 0.0
    total_stock_value_rsp: float = 0.0
    total_branches: int = 0
    total_unique_skus: int = 0

class FetchStockInventoryResponse(BaseModel):
    data: List[StockInventoryItem]
    total_count: int
    summary_stats: Optional[StockInventorySummaryStats] = None
    success: bool = True
    message: Optional[str] = None

class StockInventoryTableOption(BaseModel):
    table_name: str
    report_date: str
    display_name: str

class StockInventoryTablesResponse(BaseModel):
    tables: List[StockInventoryTableOption]
    success: bool = True

class StockInventoryFilterOptions(BaseModel):
    departments: List[Dict[str, str]] = []
    brands: List[Dict[str, str]] = []
    vendors: List[Dict[str, str]] = []

class StockInventoryExportRequest(BaseModel):
    table_name: str
    branch_codes: Optional[List[str]] = None
    sku_codes: Optional[List[str]] = None
    sku_search_type: str = "armscode"  # armscode, mcode, or artno
    departments: Optional[List[str]] = None
    brands: Optional[List[str]] = None
    vendors: Optional[List[str]] = None

# --- Mall Sales Upload Models ---

class MallSalesUploadRequest(BaseModel):
    mall_code: str
    target_date: str = Field(..., pattern=r'^\d{4}-\d{2}-\d{2}$')

class MallSalesUploadRangeRequest(BaseModel):
    mall_code: str
    start_date: str = Field(..., pattern=r'^\d{4}-\d{2}-\d{2}$')
    end_date: str = Field(..., pattern=r'^\d{4}-\d{2}-\d{2}$')

class MallSalesUploadResponse(BaseModel):
    success: bool
    message: str
    date: str
    mall_code: str
    filename: Optional[str] = None
    filepath: Optional[str] = None
    uploaded: bool
    error: Optional[str] = None

class MallSalesUploadRangeResponse(BaseModel):
    success: bool
    message: str
    total_dates: int
    successful_uploads: int
    failed_uploads: int
    results: List[MallSalesUploadResponse]

class MallSalesData(BaseModel):
    date: str
    branch_code: str
    branch_name: str
    receipt_sales_amount: float
    total_sales_amount: float

class MallSalesPreviewResponse(BaseModel):
    success: bool
    data: List[MallSalesData]
    total_records: int
    total_sales_amount: float
    message: str

class MallUploadHistory(BaseModel):
    id: str
    mall_code: str
    upload_date: str
    target_date: str
    filename: str
    filepath: str
    backup_filepath: Optional[str] = ""  # Add backup file path
    file_size: int
    upload_status: str  # 'success', 'failed', 'partial'
    ftp_uploaded: bool
    error_message: Optional[str] = None
    created_at: str

class MallUploadHistoryResponse(BaseModel):
    success: bool
    data: List[MallUploadHistory]
    total_records: int
    message: str

class MallUploadHistoryDeleteRequest(BaseModel):
    history_ids: List[str]
    delete_files: bool = True

class MallFilePreviewRequest(BaseModel):
    mall_code: str
    file_path: str
    max_rows: Optional[int] = 100

class MallFilePreviewResponse(BaseModel):
    success: bool
    data: List[List[Any]]  # CSV data as list of lists
    total_rows: int
    preview_rows: int
    file_size: int
    file_modified: str
    columns: int
    message: Optional[str] = None
    error: Optional[str] = None

class MallBackupFile(BaseModel):
    filename: str
    filepath: str
    file_size: int
    created_at: str
    modified_at: str

class MallBackupFilesResponse(BaseModel):
    success: bool
    data: List[MallBackupFile]
    total_files: int
    message: str

# --- GRN Details Report Models --- #

class GRNDetailItem(BaseModel):
    # All fields from grn_details table
    id: Optional[int] = None
    branch_id: Optional[int] = None
    branch_code: Optional[str] = None
    branch_name: Optional[str] = None
    grn_no: Optional[str] = None
    rcv_date: Optional[date] = None
    grn_type: Optional[str] = None
    grn_vendor_code: Optional[str] = None
    grn_vendor_name: Optional[str] = None
    artno_mcode: Optional[str] = None
    sku_id: Optional[int] = None
    armscode: Optional[str] = None
    mcode: Optional[str] = None
    department: Optional[str] = None
    category: Optional[str] = None
    type_of_silhouette: Optional[str] = None
    silhouette: Optional[str] = None
    gender: Optional[str] = None
    age: Optional[str] = None
    brand: Optional[str] = None
    item_vendor_code: Optional[str] = None
    item_vendor_name: Optional[str] = None
    art_no_description: Optional[str] = None
    sku_description_sz_cl: Optional[str] = None
    color: Optional[str] = None
    size: Optional[str] = None
    season_code: Optional[str] = None
    item_cost_price: Optional[Union[Decimal, str]] = None  # Can be Decimal or permission string
    rsp_price: Optional[Decimal] = None
    rsp_discount: Optional[str] = None
    selling_price: Optional[Decimal] = None
    grn_selling_price: Optional[Decimal] = None
    receive_cost: Optional[Union[Decimal, str]] = None  # Can be Decimal or permission string
    receive_ctn: Optional[Decimal] = None
    receive_qty: Optional[Decimal] = None
    po_qty: Optional[Decimal] = None
    po_cost: Optional[Union[Decimal, str]] = None  # Can be Decimal or permission string
    variance_qty_pcs_po: Optional[Decimal] = None
    grn_status: Optional[str] = None
    grn_approve: Optional[str] = None
    supplier_po: Optional[str] = None
    grn_do_no: Optional[str] = None
    do_invoice: Optional[str] = None
    other: Optional[str] = None
    ibt_from_branch_code: Optional[str] = None
    created_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class GRNSummaryItem(BaseModel):
    # Summary fields grouped by grn_no
    branch_code: Optional[str] = None
    branch_name: Optional[str] = None
    grn_no: Optional[str] = None
    rcv_date: Optional[date] = None
    grn_type: Optional[str] = None
    grn_vendor_name: Optional[str] = None
    total_grn_selling_price: Optional[Decimal] = None
    total_receive_cost: Optional[Union[Decimal, str]] = None  # Can be Decimal or permission string
    total_receive_ctn: Optional[Decimal] = None
    total_receive_qty: Optional[Decimal] = None
    total_po_qty: Optional[Decimal] = None
    total_po_cost: Optional[Union[Decimal, str]] = None  # Can be Decimal or permission string
    total_variance_qty_pcs_po: Optional[Decimal] = None
    grn_status: Optional[str] = None
    grn_approve: Optional[str] = None
    supplier_po: Optional[str] = None
    grn_do_no: Optional[str] = None
    do_invoice: Optional[str] = None
    other: Optional[str] = None

    class Config:
        from_attributes = True

class GRNFilterOption(BaseModel):
    id: str
    name: str

class GRNFilterOptionsResponse(BaseModel):
    grn_types: List[GRNFilterOption] = []
    grn_statuses: List[GRNFilterOption] = []
    grn_approve_options: List[GRNFilterOption] = []
    vendors: List[GRNFilterOption] = []

class GRNSummaryStats(BaseModel):
    grand_total_grn_selling_price: float = 0.0  # Always visible (not a cost field)
    grand_total_receive_cost: Optional[float] = None  # Made optional for cost filtering
    grand_total_receive_ctn: float = 0.0
    grand_total_receive_qty: float = 0.0
    grand_total_po_qty: float = 0.0
    grand_total_po_cost: Optional[float] = None  # Made optional for cost filtering
    grand_total_variance_qty_pcs_po: float = 0.0
    total_unique_grns: int = 0
    total_unique_items: int = 0

class FetchGRNDetailsResponse(BaseModel):
    data: Union[List[GRNDetailItem], List[GRNSummaryItem]]
    total_count: int
    summary_stats: Optional[GRNSummaryStats] = None
    success: bool = True
    message: Optional[str] = None

class GRNLatestDateResponse(BaseModel):
    latest_date: Optional[str] = None
    message: Optional[str] = None
    success: bool = True

class GRNDetailsExportRequest(BaseModel):
    view_type: str = "summary"  # "summary" or "detail"
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    branch_codes: Optional[List[str]] = None
    grn_type: Optional[str] = None
    grn_status: Optional[str] = None
    grn_approve: Optional[str] = None
    vendors: Optional[List[str]] = None 

# Add new models for multiple DO checker functionality at the end of the file, before the final comment

# Multiple DO Checker Models
class MultipleDOCheckerRequest(BaseModel):
    do_numbers: List[str] = Field(..., description="List of DO numbers to check")

class MultipleDOSummaryItem(BaseModel):
    """Summary item for multiple DO checker table view"""
    do_no: Optional[str] = None
    do_date: Optional[str] = None
    from_branch_code: Optional[str] = None
    from_branch: Optional[str] = None
    to_branch_code: Optional[str] = None
    to_branch: Optional[str] = None
    do_status: Optional[str] = None
    do_approved: Optional[str] = None
    checkout_status: Optional[str] = None
    gr_status: Optional[str] = None
    total_do_qty: Optional[float] = None
    total_ctn: Optional[float] = None
    total_do_item_cost: Optional[Union[float, str]] = None  # Can be float or permission string
    # Status indicators for quick view
    has_gr_data: Optional[bool] = False
    has_variance: Optional[bool] = False
    error_message: Optional[str] = None  # For DOs that couldn't be found

class MultipleDOCheckerResponse(BaseModel):
    """Response model for multiple DO checker"""
    results: List[MultipleDOSummaryItem]
    total_found: int
    total_requested: int
    success: bool = True
    message: Optional[str] = None

# Wildcard DO Search Models
class WildcardDOSearchRequest(BaseModel):
    search_pattern: str = Field(..., description="Partial DO number pattern to search for")
    limit: Optional[int] = Field(default=None, description="Maximum number of results to return (None for unlimited)")

class WildcardDOSearchResponse(BaseModel):
    results: List[MultipleDOSummaryItem]
    total_found: int
    search_pattern: str
    success: bool
    message: Optional[str] = None 

    class Config:
        from_attributes = True

# Pending Receiving DO Models
class PendingReceivingDORequest(BaseModel):
    limit: Optional[int] = Field(default=50, description="Maximum number of results to return")

class PendingReceivingDOResponse(BaseModel):
    results: List[MultipleDOSummaryItem]
    total_found: int
    success: bool
    message: Optional[str] = None

    class Config:
        from_attributes = True

# --- Module Permission Models (for cost permissions) --- #

class ModulePermission(BaseModel):
    module_id: str
    module_name: str
    description: Optional[str] = None

class UserModulePermission(BaseModel):
    id: int
    user_id: int
    module_id: str
    created_at: datetime

    class Config:
        from_attributes = True

class UserModulePermissionsResponse(BaseModel):
    modules: List[str]  # List of module_ids the user has

class ModulePermissionAssignRequest(BaseModel):
    user_id: int
    module_ids: List[str]  # List of module_ids to assign

class ModulePermissionAssignResponse(BaseModel):
    success: bool
    message: str
    assigned_modules: List[str] 

# --- PO Checker Models --- #

class POCheckerSkuDetail(BaseModel):
    """Individual SKU detail within a color group"""
    mcode: Optional[str] = None
    size: Optional[str] = None
    qty: Optional[float] = None
    armscode: Optional[str] = None
    sku_description_sz_cl: Optional[str] = None
    po_cost: Optional[Union[float, str]] = None  # Can be float or permission string
    cost_price: Optional[Union[float, str]] = None  # Can be float or permission string
    rsp_price: Optional[float] = None
    selling_price: Optional[float] = None
    grn_cost: Optional[Union[float, str]] = None  # Can be float or permission string
    grn_selling_price: Optional[float] = None

    class Config:
        from_attributes = True

class POCheckerSkuPivotedData(BaseModel):
    unique_sizes: List[str]
    color_data: List[Dict[str, Any]]  # Each dict: {"color": "Black", "sku_details": [...], "size1": qty1, ...}
    art_no_description: Optional[str] = None
    brand: Optional[str] = None
    department: Optional[str] = None
    category: Optional[str] = None
    type_of_silhouette: Optional[str] = None
    silhouette: Optional[str] = None
    gender: Optional[str] = None
    age: Optional[str] = None
    total_qty: Optional[float] = None

class POCheckerSummaryItem(BaseModel):
    # Basic PO Information
    po_date: Optional[date] = None
    po_no: Optional[str] = None
    po_branch_code: Optional[str] = None
    po_branch_name: Optional[str] = None
    hq_master_po: Optional[str] = None
    vendor_code: Optional[str] = None
    vendor_name: Optional[str] = None
    delivery_date: Optional[date] = None
    cancel_date: Optional[date] = None
    po_remark: Optional[str] = None
    po_remark2: Optional[str] = None
    
    # Status Information
    po_status: Optional[str] = None
    po_active: Optional[str] = None
    po_approved: Optional[str] = None
    po_delivered: Optional[str] = None
    
    # PO Quantities and Costs
    total_po_ordered_ctn: Optional[float] = None
    total_po_ordered_pcs: Optional[float] = None
    total_po_netamt: Optional[Union[float, str]] = None  # Can be float or permission string (cost field)
    total_po_rsp: Optional[float] = None
    gross_profit_rm: Optional[Union[float, str]] = None  # Can be float or permission string (cost field)
    
    # GR Information  
    gr_status: Optional[str] = None
    grn_rcv_date: Optional[date] = None
    grn_no: Optional[str] = None
    grn_approve: Optional[str] = None
    grn_doc_details: Optional[str] = None
    total_grn_ctn: Optional[float] = None
    total_grn_pcs: Optional[float] = None
    total_grn_cost: Optional[Union[float, str]] = None  # Can be float or permission string (cost field)
    
    # Variance Information
    total_variance_qty_ctn: Optional[float] = None
    total_variance_qty_pcs: Optional[float] = None
    total_variance_cost: Optional[Union[float, str]] = None  # Can be float or permission string (cost field)

    @validator("po_date", "delivery_date", "cancel_date", "grn_rcv_date", pre=True)
    def ensure_date_type(cls, value):
        if isinstance(value, datetime):
            return value.date()
        if isinstance(value, str):
            if not value.strip():
                return None
            # Try multiple date formats
            date_formats = [
                "%Y-%m-%d",      # ISO format: 2024-01-03
                "%m/%d/%Y",      # US format: 03/01/2024
                "%d/%m/%Y",      # EU format: 03/01/2024
                "%Y/%m/%d",      # Alternative: 2024/01/03
            ]
            
            for fmt in date_formats:
                try:
                    return datetime.strptime(value, fmt).date()
                except ValueError:
                    continue
            
            # If no format works, return None instead of the invalid string
            return None
        return value

class POCheckerResponse(BaseModel):
    summary: Optional[POCheckerSummaryItem] = None
    pivoted_sku_data: Optional[Dict[str, POCheckerSkuPivotedData]] = None  # Keyed by artno
    message: Optional[str] = None
# Multiple PO Checker Models
class MultiplePOCheckerRequest(BaseModel):
    po_numbers: List[str] = Field(..., description="List of PO numbers to check")

class MultiplePOSummaryItem(BaseModel):
    """Summary item for multiple PO checker table view"""
    po_no: Optional[str] = None
    po_date: Optional[str] = None
    po_branch_code: Optional[str] = None
    po_branch_name: Optional[str] = None
    hq_master_po: Optional[str] = None
    vendor_code: Optional[str] = None
    vendor_name: Optional[str] = None
    po_status: Optional[str] = None
    po_active: Optional[str] = None
    po_approved: Optional[str] = None
    po_delivered: Optional[str] = None
    gr_status: Optional[str] = None
    total_po_ordered_ctn: Optional[float] = None
    total_po_ordered_pcs: Optional[float] = None
    total_po_netamt: Optional[Union[float, str]] = None  # Can be float or permission string (cost field)
    total_po_rsp: Optional[float] = None
    gross_profit_rm: Optional[Union[float, str]] = None  # Can be float or permission string (cost field)
    total_grn_cost: Optional[Union[float, str]] = None  # Can be float or permission string (cost field)
    total_variance_cost: Optional[Union[float, str]] = None  # Can be float or permission string (cost field)
    grn_doc_details: Optional[str] = None
    # Status indicators for quick view
    has_gr_data: Optional[bool] = False
    has_variance: Optional[bool] = False
    error_message: Optional[str] = None  # For POs that couldn't be found

class MultiplePOCheckerResponse(BaseModel):
    """Response model for multiple PO checker"""
    results: List[MultiplePOSummaryItem]
    total_found: int
    total_requested: int
    success: bool = True
    message: Optional[str] = None

# Wildcard PO Search Models
class WildcardPOSearchRequest(BaseModel):
    search_pattern: str = Field(..., description="Partial PO number pattern to search for")
    limit: Optional[int] = Field(default=50, description="Maximum number of results to return")

class WildcardPOSearchResponse(BaseModel):
    results: List[MultiplePOSummaryItem]
    total_found: int
    search_pattern: str
    success: bool
    message: Optional[str] = None

# Remark PO Search Models
class RemarkPOSearchRequest(BaseModel):
    remark_pattern: str = Field(..., description="Remark pattern to search for")
    limit: Optional[int] = Field(default=50, description="Maximum number of results to return")

class RemarkPOSearchResponse(BaseModel):
    results: List[MultiplePOSummaryItem]
    total_found: int
    remark_pattern: str
    success: bool
    message: Optional[str] = None

# --- GR Checker Models --- #

class GRCheckerSkuDetail(BaseModel):
    """Individual SKU detail within a color group"""
    mcode: Optional[str] = None
    size: Optional[str] = None
    qty: Optional[float] = None
    armscode: Optional[str] = None
    sku_description_sz_cl: Optional[str] = None
    receive_cost: Optional[Union[float, str]] = None  # Can be float or permission string
    grn_selling_price: Optional[float] = None

    class Config:
        from_attributes = True

class GRCheckerSkuPivotedData(BaseModel):
    unique_sizes: List[str]
    color_data: List[Dict[str, Any]]  # Each dict: {"color": "Black", "sku_details": [...], "size1": qty1, ...}
    art_no_description: Optional[str] = None
    brand: Optional[str] = None
    department: Optional[str] = None
    category: Optional[str] = None
    gender: Optional[str] = None
    total_qty: Optional[float] = None

    class Config:
        from_attributes = True

class GRCheckerSummaryItem(BaseModel):
    # Basic GR Information
    grn_no: Optional[str] = None
    grn_date: Optional[str] = None
    grn_type: Optional[str] = None
    branch_code: Optional[str] = None
    branch_name: Optional[str] = None
    grn_vendor_code: Optional[str] = None
    grn_vendor_name: Optional[str] = None
    ibt_from_branch_code: Optional[str] = None
    
    # Status Information
    grn_status: Optional[str] = None
    grn_approve: Optional[str] = None
    
    # Document References
    supplier_po: Optional[str] = None
    grn_do_no: Optional[str] = None
    do_invoice: Optional[str] = None
    other: Optional[str] = None
    
    # Quantity and Cost Totals
    total_receive_qty: Optional[float] = None
    total_receive_ctn: Optional[float] = None
    total_receive_cost: Optional[Union[float, str]] = None  # Can be float or permission string (cost field)
    total_grn_selling_price: Optional[float] = None
    total_po_qty: Optional[float] = None
    total_po_cost: Optional[Union[float, str]] = None  # Can be float or permission string (cost field)
    
    # Variances
    total_variance_qty_pcs: Optional[float] = None
    total_variance_cost: Optional[Union[float, str]] = None  # Can be float or permission string (cost field)



    class Config:
        from_attributes = True

class GRCheckerResponse(BaseModel):
    summary: Optional[GRCheckerSummaryItem] = None
    pivoted_sku_data: Optional[Dict[str, GRCheckerSkuPivotedData]] = None  # Keyed by artno
    message: Optional[str] = None

    class Config:
        from_attributes = True

class MultipleGRCheckerRequest(BaseModel):
    grn_numbers: List[str] = Field(..., description="List of GRN numbers to check")

class MultipleGRSummaryItem(BaseModel):
    """Summary item for multiple GR checker table view"""
    grn_no: Optional[str] = None
    grn_date: Optional[str] = None
    branch_code: Optional[str] = None
    branch_name: Optional[str] = None
    grn_type: Optional[str] = None
    grn_vendor_name: Optional[str] = None
    grn_status: Optional[str] = None
    grn_approve: Optional[str] = None
    total_receive_qty: Optional[float] = None
    total_receive_cost: Optional[Union[float, str]] = None  # Can be float or permission string (cost field)
    total_grn_selling_price: Optional[float] = None
    ibt_from_branch_code: Optional[str] = None
    # Status indicators for quick view
    has_variance: Optional[bool] = False
    error_message: Optional[str] = None  # For GRNs that couldn't be found

    class Config:
        from_attributes = True

class MultipleGRCheckerResponse(BaseModel):
    """Response model for multiple GR checker"""
    results: List[MultipleGRSummaryItem]
    total_found: int
    total_requested: int
    success: bool = True
    message: Optional[str] = None

    class Config:
        from_attributes = True

class WildcardGRSearchRequest(BaseModel):
    search_pattern: str = Field(..., description="Partial GRN number pattern to search for")
    limit: Optional[int] = Field(default=50, description="Maximum number of results to return")

class WildcardGRSearchResponse(BaseModel):
    results: List[MultipleGRSummaryItem]
    total_found: int
    search_pattern: str
    success: bool
    message: Optional[str] = None

    class Config:
        from_attributes = True 

# --- Dashboard Models --- #

class PendingReceivingDOStats(BaseModel):
    """Statistics for Pending Receiving DO dashboard card"""
    pending_count: int
    affected_branches: int
    oldest_do_date: Optional[str] = None
    newest_do_date: Optional[str] = None
    message: str

class PendingApprovalDOStats(BaseModel):
    """Statistics for Pending Approval DO dashboard card"""
    pending_count: int
    affected_branches: int
    oldest_do_date: Optional[str] = None
    newest_do_date: Optional[str] = None
    message: str

class PendingReceivingDODetail(BaseModel):
    """Individual DO detail for dashboard drill-down"""
    do_id: Optional[int] = None
    active: Optional[str] = None
    do_date: Optional[str] = None
    do_no: Optional[str] = None
    do_status: Optional[str] = None
    do_approved: Optional[str] = None
    from_branch_code: Optional[str] = None
    from_branch: Optional[str] = None
    to_branch_code: Optional[str] = None
    to_branch: Optional[str] = None
    checkout_status: Optional[str] = None
    gr_status: Optional[str] = None
    grn_approve: Optional[str] = None

    class Config:
        from_attributes = True

class DashboardResponse(BaseModel):
    """Generic dashboard response wrapper"""
    success: bool = True
    data: Optional[Dict[str, Any]] = None
    message: Optional[str] = None
    error: Optional[str] = None 

# Add the new DO Checker export request model after the existing DO Checker models

class DOCheckerSearchExportRequest(BaseModel):
    """Request model for DO Checker search export with summary/details options"""
    search_type: str = Field(..., description="Type of search: 'single', 'multiple', or 'wildcard'")
    export_type: str = Field(..., description="Type of export: 'summary' or 'details'")
    # Search parameters
    do_no: Optional[str] = Field(None, description="DO number for single search")
    do_numbers: Optional[List[str]] = Field(None, description="List of DO numbers for multiple search")
    search_pattern: Optional[str] = Field(None, description="Search pattern for wildcard search")
    limit: Optional[int] = Field(50, description="Limit for wildcard/multiple searches")

    class Config:
        from_attributes = True 

class BulkImageUploadWithTemplateResponse(BaseModel):
    total_requested: int
    successful_uploads: int
    failed_uploads: int
    results: List[BulkImageUploadResult]
    success: bool = True
    message: str

class SkuImageDownloadRequest(BaseModel):
    image_ids: List[int]
    artno: str

class SkuImageDownloadResponse(BaseModel):
    success: bool
    message: str
    zip_path: Optional[str] = None
    zip_filename: Optional[str] = None

# --- Password Reset Models ---
class PasswordResetRequest(BaseModel):
    email: EmailStr

class PasswordResetConfirm(BaseModel):
    token: str
    new_password: str = Field(..., min_length=8, description="New password (minimum 8 characters)")

class PasswordResetResponse(BaseModel):
    success: bool = True
    message: str
    error: Optional[str] = None

class TokenValidationResponse(BaseModel):
    success: bool = True
    valid: bool
    username: Optional[str] = None
    message: Optional[str] = None
    error: Optional[str] = None

# --- Email Notification Models ---
class EmailNotificationRequest(BaseModel):
    to_email: EmailStr
    title: str
    message: str
    action_url: Optional[str] = None

class EmailTestRequest(BaseModel):
    to_email: EmailStr
    test_type: str = Field(default="connection", description="Type of test: 'connection' or 'full'")

class EmailTestResponse(BaseModel):
    success: bool
    message: str
    test_type: str
    method_used: Optional[str] = None  # 'api' or 'smtp'
    error: Optional[str] = None

# --- Stock Take Models ---

class StockTakeSkuMasterItem(BaseModel):
    """SKU master data item for stock take offline storage"""
    active_status: Optional[str] = None
    sku_item_code: str
    art_no: Optional[str] = None
    mcode: Optional[str] = None
    art_no_description: Optional[str] = None
    brand: Optional[str] = None
    department: Optional[str] = None
    category: Optional[str] = None
    type_of_silhouette: Optional[str] = None
    silhouette: Optional[str] = None
    color: Optional[str] = None
    size: Optional[str] = None
    vendor: Optional[str] = None
    season_code: Optional[str] = None
    rsp_price: Optional[float] = None
    rsp_discount: Optional[float] = None
    lastupdate: Optional[str] = None
    is_parent: Optional[str] = None

class StockTakeSkuMasterResponse(BaseModel):
    """Response for SKU master data download"""
    skus: List[StockTakeSkuMasterItem]
    total_count: int
    limit: Optional[int] = None
    offset: int = 0
    has_more: bool = False

class StockTakeSessionCreate(BaseModel):
    """Request to create a new stock take session"""
    branch_code: str
    session_name: str
    session_date: str  # ISO date string
    notes: Optional[str] = ""

class StockTakeSession(BaseModel):
    """Stock take session data"""
    id: str
    branch_code: str
    user_id: int
    session_name: str
    session_date: str  # ISO date string
    status: str  # 'in_progress', 'completed', 'synced'
    notes: Optional[str] = None
    created_at: str  # ISO datetime string
    synced_at: Optional[str] = None  # ISO datetime string

class StockTakeSessionsResponse(BaseModel):
    """Response for getting user's stock take sessions"""
    sessions: List[StockTakeSession]

class StockTakeItem(BaseModel):
    """Individual stock take count item"""
    sku_item_code: str
    counted_quantity: int = 0
    system_quantity: int = 0
    notes: Optional[str] = ""

class StockTakeItemWithDetails(BaseModel):
    """Stock take item with full details"""
    id: int
    session_id: str
    sku_item_code: str
    counted_quantity: int
    system_quantity: int
    variance: int
    notes: Optional[str] = None
    counted_at: str  # ISO datetime string
    created_at: str  # ISO datetime string

class StockTakeItemsRequest(BaseModel):
    """Request to save stock take items"""
    items: List[StockTakeItem]

class StockTakeItemsResponse(BaseModel):
    """Response for saving stock take items"""
    success: bool
    saved_count: int
    total_items: int

class StockTakeItemsListResponse(BaseModel):
    """Response for getting stock take items"""
    items: List[StockTakeItemWithDetails]

# --- Stock Take Models ---

class StockTakeSkuMasterItem(BaseModel):
    """SKU master data item for stock take offline storage"""
    active_status: Optional[str] = None
    sku_item_code: str
    art_no: Optional[str] = None
    mcode: Optional[str] = None
    art_no_description: Optional[str] = None
    brand: Optional[str] = None
    department: Optional[str] = None
    category: Optional[str] = None
    type_of_silhouette: Optional[str] = None
    silhouette: Optional[str] = None
    color: Optional[str] = None
    size: Optional[str] = None
    vendor: Optional[str] = None
    season_code: Optional[str] = None
    rsp_price: Optional[Union[float, str]] = None  # Can be float or permission string
    rsp_discount: Optional[Union[str, str]] = None  # Can be string or permission string
    lastupdate: Optional[datetime] = None
    is_parent: Optional[str] = None

    class Config:
        from_attributes = True

class StockTakeMasterDataResponse(BaseModel):
    """Response for SKU master data download"""
    results: List[StockTakeSkuMasterItem]
    total_records: int
    limit: Optional[int] = None
    offset: int = 0
    has_more: bool = False
    success: bool = True
    error: Optional[str] = None

class StockTakeMasterDataVersionResponse(BaseModel):
    """Response for master data version check"""
    version: Optional[str] = None
    total_skus: int
    generated_at: str
    success: bool = True
    error: Optional[str] = None

class StockTakeSessionCreate(BaseModel):
    """Request to create a new stock take session"""
    branch_code: str
    session_name: str
    notes: Optional[str] = None

class StockTakeSession(BaseModel):
    """Stock take session model"""
    session_id: str
    branch_code: str
    session_name: str
    session_date: str  # ISO date string
    status: str  # 'in_progress', 'completed', 'synced'
    notes: Optional[str] = None
    created_at: str  # ISO datetime string
    synced_at: Optional[str] = None  # ISO datetime string
    item_count: int = 0

    class Config:
        from_attributes = True

class StockTakeSessionsResponse(BaseModel):
    """Response for user stock take sessions"""
    sessions: List[StockTakeSession]
    success: bool = True
    error: Optional[str] = None

class StockTakeItemSubmit(BaseModel):
    """Individual stock take item for submission"""
    sku_item_code: str
    mcode: Optional[str] = None
    counted_quantity: int
    system_quantity: Optional[int] = 0
    location: Optional[str] = None  # Warehouse location
    shelf: Optional[str] = None     # Shelf identifier
    notes: Optional[str] = None
    # SKU Detail Fields for offline support
    art_no: Optional[str] = None
    art_no_description: Optional[str] = None
    brand: Optional[str] = None
    department: Optional[str] = None
    category: Optional[str] = None
    type_of_silhouette: Optional[str] = None
    color: Optional[str] = None
    size: Optional[str] = None
    active_status: Optional[str] = None
    counted_at: Optional[str] = None  # ISO datetime string

class StockTakeItemsSubmitRequest(BaseModel):
    """Request to submit stock take items"""
    session_id: str
    items: List[StockTakeItemSubmit]

class StockTakeItemsSubmitResponse(BaseModel):
    """Response for stock take items submission"""
    success: bool = True
    inserted_count: int = 0
    updated_count: int = 0
    total_processed: int = 0
    message: Optional[str] = None
    error: Optional[str] = None

class StockTakeItem(BaseModel):
    """Stock take item model"""
    sku_item_code: str
    mcode: Optional[str] = None
    counted_quantity: int
    system_quantity: int
    variance: int
    location: Optional[str] = None  # Warehouse location
    shelf: Optional[str] = None     # Shelf identifier
    notes: Optional[str] = None
    # SKU Detail Fields for offline support
    art_no: Optional[str] = None
    art_no_description: Optional[str] = None
    brand: Optional[str] = None
    department: Optional[str] = None
    category: Optional[str] = None
    type_of_silhouette: Optional[str] = None
    color: Optional[str] = None
    size: Optional[str] = None
    active_status: Optional[str] = None
    counted_at: Optional[str] = None  # ISO datetime string
    created_at: Optional[str] = None  # ISO datetime string

class StockTakeSessionItemsResponse(BaseModel):
    """Response for stock take session items"""
    items: List[StockTakeItem]
    total_records: int
    limit: Optional[int] = None
    offset: int = 0
    has_more: bool = False
    success: bool = True
    error: Optional[str] = None

class StockTakeSessionCompleteResponse(BaseModel):
    """Response for completing a stock take session"""
    session_id: str
    status: str
    synced_at: str
    success: bool = True
    error: Optional[str] = None

class StockTakeMasterDataRequest(BaseModel):
    """Request for master data download with pagination"""
    limit: Optional[int] = None
    offset: int = 0
    search_term: Optional[str] = None

# --- Stock Take Export Models ---

class StockTakeExportRequest(BaseModel):
    """Request to export stock take session results"""
    session_id: str = Field(..., description="Stock take session ID to export") 

# --- QOH (Quantity On Hand) Models ---

class QOHRequest(BaseModel):
    """Request model for QOH lookup"""
    artno: Optional[str] = None
    mcode: Optional[str] = None

    @validator('artno', 'mcode')
    def at_least_one_field(cls, v, values):
        if not v and not values.get('artno') and not values.get('mcode'):
            raise ValueError('Either artno or mcode must be provided')
        return v

class QOHItem(BaseModel):
    """Individual QOH item for a specific branch and SKU"""
    branch_code: str
    branch_name: str
    artno: str
    mcode: str
    color: Optional[str] = None
    size: Optional[str] = None
    qty: Optional[int] = None
    total_unverified_qty: Optional[int] = None

    class Config:
        from_attributes = True

class QOHResponse(BaseModel):
    """Response model for QOH lookup"""
    items: List[QOHItem]
    total_records: int
    artno: Optional[str] = None
    mcode: Optional[str] = None
    table_name: str  # Name of the stock table used
    success: bool = True
    message: Optional[str] = None
    error: Optional[str] = None

    class Config:
        from_attributes = True 

# --- SKU Sales Summary Models ---

class SkuSalesBranchDetail(BaseModel):
    branch_code: str
    branch_name: str
    total_qty: int
    total_sales: Decimal

    class Config:
        from_attributes = True

class SkuSalesSummaryResponse(BaseModel):
    """Response model for SKU sales summary"""
    artno: str
    mcode: Optional[str] = None  # Optional for parent code searches (all children)
    total_qty_sold: int
    branch_count: int
    first_sale_date: Optional[str] = None
    last_sale_date: Optional[str] = None
    branch_details: List[SkuSalesBranchDetail]
    success: bool = True
    message: Optional[str] = None
    error: Optional[str] = None

    class Config:
        from_attributes = True

class QOHRequest(BaseModel):
    """Request model for QOH (Quantity on Hand) lookup"""
    artno: str
    branch_code: Optional[str] = None

    class Config:
        from_attributes = True

class QOHResponse(BaseModel):
    """Response model for QOH (Quantity on Hand) lookup"""
    artno: str
    branch_code: Optional[str] = None
    branch_name: Optional[str] = None
    qoh: int
    success: bool = True
    message: Optional[str] = None
    error: Optional[str] = None

    class Config:
        from_attributes = True


# --- PO Notification Models ---

class PONotificationRequest(BaseModel):
    hq_po_id: str

    class Config:
        from_attributes = True

class PONotificationSummary(BaseModel):
    hq_po_id: str
    po_date: Optional[str] = None
    vendor_name: Optional[str] = None
    master_po_total_amount: Optional[Union[Decimal, str]] = None
    master_po_total_qty: Optional[Union[Decimal, str]] = None

class PONotificationItem(BaseModel):
    po_no: str
    po_branch_code: Optional[str] = None
    po_branch_name: Optional[str] = None
    po_delivered: Optional[str] = None
    delivery_date: Optional[str] = None
    supplier_delivery_note: Optional[str] = None
    po_date: Optional[str] = None
    vendor_name: Optional[str] = None
    total_po_ordered_pcs: Optional[Union[int, Decimal, str]] = None
    total_po_netamt: Optional[Union[Decimal, str]] = None
    contact_email: Optional[str] = None
    
    @validator('total_po_ordered_pcs', pre=True, always=True)
    def convert_quantity_to_int(cls, v):
        if v is None:
            return None
        if isinstance(v, str):
            return v  # Keep permission strings as-is
        if isinstance(v, (int, float, Decimal)):
            return int(v)  # Convert to integer
        return v
    
    class Config:
        from_attributes = True

class PONotificationResponse(BaseModel):
    success: bool
    message: str
    hq_po_id: Optional[str] = None
    summary: Optional[PONotificationSummary] = None
    po_data: Optional[List[PONotificationItem]] = None

    class Config:
        from_attributes = True

class PONotificationEmailRequest(BaseModel):
    hq_po_id: str
    po_data: List[PONotificationItem]
    cc_emails: Optional[List[str]] = None
    override_email: Optional[List[str]] = None
    summary: Optional[PONotificationSummary] = None
    custom_subject: Optional[str] = None
    custom_message: Optional[str] = None

    class Config:
        from_attributes = True

class PONotificationEmailResponse(BaseModel):
    success: bool
    message: str
    po_count: int
    po_numbers: List[str]

    class Config:
        from_attributes = True

# Bulk PO Notification Models
class BulkPONotificationRequest(BaseModel):
    hq_po_ids: List[str]

    class Config:
        from_attributes = True

class BulkPONotificationResponse(BaseModel):
    success: bool
    message: str
    results: Dict[str, PONotificationResponse]
    total_found: int
    total_searched: int

    class Config:
        from_attributes = True

class BulkPONotificationEmailRequest(BaseModel):
    hq_po_ids: List[str]
    bulk_results: Dict[str, Any]  # The bulk search results
    cc_emails: Optional[List[str]] = None
    override_email: Optional[List[str]] = None
    custom_subject: Optional[str] = None
    custom_message: Optional[str] = None

    class Config:
        from_attributes = True

class BulkPONotificationEmailResponse(BaseModel):
    success: bool
    message: str
    total_emails_sent: int
    total_pos_processed: int
    results: Dict[str, Any]  # Detailed results per Master PO

    class Config:
        from_attributes = True

# --- PO Management Models ---

class POManagementVendor(BaseModel):
    vendor_code: str
    vendor_name: str

    class Config:
        from_attributes = True

class POManagementVendorsResponse(BaseModel):
    vendors: List[POManagementVendor]

class POManagementBranch(BaseModel):
    branch_code: str
    branch_name: str

    class Config:
        from_attributes = True

class POManagementBranchesResponse(BaseModel):
    branches: List[POManagementBranch]

class ExcelValidationResponse(BaseModel):
    valid: bool
    error: Optional[str] = None
    columns: Optional[List[str]] = None
    sample_data: Optional[List[Dict[str, Any]]] = None
    total_rows: Optional[int] = None

class POManagementUploadResponse(BaseModel):
    success: bool
    message: str
    csv_count: Optional[int] = None
    branch_codes: Optional[List[str]] = None

class CSVTemplateResponse(BaseModel):
    template_headers: List[str]
    sample_data: List[Dict[str, Any]]

class SkuValidationRequest(BaseModel):
    sku_codes: List[str]

class SkuValidationResponse(BaseModel):
    valid: bool
    message: str
    valid_skus: List[str]
    invalid_skus: List[str]
    duplicate_skus: List[str]
    total_checked: int
    valid_count: int
    invalid_count: int
    duplicate_count: int

class BranchValidationRequest(BaseModel):
    branch_codes: List[str]

class BranchValidationResponse(BaseModel):
    valid: bool
    message: str
    valid_branches: List[str]
    invalid_branches: List[str]
    duplicate_branches: List[str]
    total_checked: int
    valid_count: int
    invalid_count: int
    duplicate_count: int

class BranchRemarkValidationResponse(BaseModel):
    valid: bool
    message: str
    empty_count: int
    total_rows: int

class UnifiedValidationResponse(BaseModel):
    sku_validation: Optional[Dict[str, Any]]
    branch_validation: Optional[Dict[str, Any]]
    branch_remark_validation: Optional[Dict[str, Any]]
    all_valid: bool
    errors: List[str]
