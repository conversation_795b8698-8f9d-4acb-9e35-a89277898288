"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { useState } from "react";
import {
  <PERSON><PERSON>ontainer,
  ProCard,
  ProTable
} from "@ant-design/pro-components";
import {
  Button,
  Input,
  message,
  Modal,
  Form,
  Tag,
  Typography,
  Descriptions,
  Alert,
  Spin,
  Checkbox,
  Select
} from "antd";
import {
  SearchOutlined,
  MailOutlined,
  EyeOutlined
} from "@ant-design/icons";
import { request } from "@umijs/max";
const { Title, Text } = Typography;
const { TextArea } = Input;
const PONotificationPage = () => {
  const [loading, setLoading] = useState(false);
  const [searchResult, setSearchResult] = useState(null);
  const [emailModalVisible, setEmailModalVisible] = useState(false);
  const [emailLoading, setEmailLoading] = useState(false);
  const [selectedPoNos, setSelectedPoNos] = useState([]);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [previewContent, setPreviewContent] = useState("");
  const [searchForm] = Form.useForm();
  const [emailForm] = Form.useForm();
  const handleSearch = async (values) => {
    if (!values.hq_po_id?.trim()) {
      message.error("Please enter an HQ PO ID");
      return;
    }
    setLoading(true);
    setSearchResult(null);
    setSelectedPoNos([]);
    try {
      const response = await request("/api/po-import/po-notification/search", {
        method: "POST",
        data: { hq_po_id: values.hq_po_id.trim() }
      });
      setSearchResult(response);
      if (response.success && response.po_data && response.po_data.length > 0) {
        message.success(response.message);
        setSelectedPoNos(response.po_data.map((po) => po.po_no));
      } else {
        message.warning(response.message || "No POs found.");
      }
    } catch (error) {
      console.error("Search error:", error);
      message.error("Failed to search for POs. Please try again.");
    } finally {
      setLoading(false);
    }
  };
  const generatePreviewContent = (values) => {
    const selectedPOs = searchResult?.po_data?.filter((po) => selectedPoNos.includes(po.po_no)) || [];
    const isOverrideMode = values.override_email && values.override_email.length > 0;
    const subject = values.custom_subject || (isOverrideMode ? `PO Notification - HQ PO ID: ${searchResult?.hq_po_id}` : `PO Notification - ${selectedPOs.length} New Purchase Order(s)`);
    const message2 = values.custom_message || (isOverrideMode ? `Please find the details for ${selectedPOs.length} Purchase Order(s) related to HQ PO ID: ${searchResult?.hq_po_id}.` : `Please find details for ${selectedPOs.length} new Purchase Order(s) assigned to your branch.`);
    return `
      <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif; line-height: 1.6; color: #333; max-width: 1200px; margin: 0 auto; padding: 20px; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);">
        <div style="text-align: center; padding: 20px 0; border-bottom: 2px solid #1890ff; margin-bottom: 30px;">
          <h1 style="font-size: 24px; font-weight: bold; color: #1890ff; margin: 0;">OC Portal System</h1>
        </div>

        <div style="padding: 0 20px;">
          <div style="font-size: 20px; margin-bottom: 20px; color: #333; font-weight: bold;">
            ${subject}
          </div>

          <div style="font-size: 16px; line-height: 1.8; margin-bottom: 30px; color: #555; white-space: pre-line;">
            ${message2}
          </div>

          <div style="margin: 30px 0;">
            <h3 style="color: #1890ff; margin-bottom: 15px; font-size: 18px;">Master PO Information</h3>
            <div style="background-color: #f8f9fa; border-radius: 6px; padding: 20px; margin-bottom: 25px;">
              <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                <div>
                  <strong style="color: #333;">Master PO No:</strong><br>
                  <span style="color: #555;">${searchResult?.hq_po_id || "N/A"}</span>
                </div>
                <div>
                  <strong style="color: #333;">PO Date:</strong><br>
                  <span style="color: #555;">${searchResult?.summary?.po_date || "N/A"}</span>
                </div>
                <div>
                  <strong style="color: #333;">Vendor:</strong><br>
                  <span style="color: #555;">${searchResult?.summary?.vendor_name || "N/A"}</span>
                </div>
                <div>
                  <strong style="color: #333;">Total Quantity:</strong><br>
                  <span style="color: #555; font-weight: bold;">${searchResult?.summary?.master_po_total_qty || "N/A"}</span>
                </div>
              </div>
            </div>
          </div>

          <div style="margin: 30px 0;">
            <h3 style="color: #1890ff; margin-bottom: 15px; font-size: 18px;">Purchase Order Details</h3>
            <div style="overflow-x: auto; margin: 20px 0; border: 1px solid #e9ecef; border-radius: 6px;">
              <table style="width: 100%; min-width: 800px; border-collapse: collapse; font-size: 13px; background-color: #fff;">
                <thead>
                  <tr style="background-color: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                    <th style="border: 1px solid #dee2e6; padding: 12px 8px; text-align: left; font-weight: 600; color: #495057;">PO No</th>
                    <th style="border: 1px solid #dee2e6; padding: 12px 8px; text-align: left; font-weight: 600; color: #495057;">Branch Code</th>
                    <th style="border: 1px solid #dee2e6; padding: 12px 8px; text-align: left; font-weight: 600; color: #495057;">Branch Name</th>
                    <th style="border: 1px solid #dee2e6; padding: 12px 8px; text-align: left; font-weight: 600; color: #495057;">Delivery Date</th>
                    <th style="border: 1px solid #dee2e6; padding: 12px 8px; text-align: left; font-weight: 600; color: #495057;">Supplier Delivery Note</th>
                    <th style="border: 1px solid #dee2e6; padding: 12px 8px; text-align: right; font-weight: 600; color: #495057;">Total Ordered Pcs</th>
                  </tr>
                </thead>
                <tbody>
                  ${selectedPOs.map((po, index) => `
                    <tr style="background-color: ${index % 2 === 0 ? "#f8f9fa" : "#ffffff"};">
                      <td style="border: 1px solid #dee2e6; padding: 10px 8px;">${po.po_no}</td>
                      <td style="border: 1px solid #dee2e6; padding: 10px 8px;">${po.po_branch_code || "N/A"}</td>
                      <td style="border: 1px solid #dee2e6; padding: 10px 8px;">${po.po_branch_name || "N/A"}</td>
                      <td style="border: 1px solid #dee2e6; padding: 10px 8px;">${po.delivery_date || "N/A"}</td>
                      <td style="border: 1px solid #dee2e6; padding: 10px 8px;">${po.supplier_delivery_note || "N/A"}</td>
                      <td style="border: 1px solid #dee2e6; padding: 10px 8px; text-align: right; font-weight: 600; color: #28a745;">${typeof po.total_po_ordered_pcs === "number" ? po.total_po_ordered_pcs.toLocaleString() : po.total_po_ordered_pcs || "0"}</td>
                    </tr>
                  `).join("")}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #e9ecef; text-align: center; font-size: 14px; color: #888;">
          <p>This email was sent from OC Portal System.<br>Please do not reply to this email as it is automatically generated.</p>
          <p>&copy; ${(/* @__PURE__ */ new Date()).getFullYear()} OC Portal System. All rights reserved.</p>
        </div>
      </div>
    `;
  };
  const handlePreviewEmail = () => {
    const values = emailForm.getFieldsValue();
    const selectedPOs = searchResult?.po_data?.filter((po) => selectedPoNos.includes(po.po_no)) || [];
    if (selectedPOs.length === 0) {
      message.error("Please select at least one PO to preview.");
      return;
    }
    try {
      const htmlContent = generatePreviewContent(values);
      setPreviewContent(htmlContent);
      setPreviewModalVisible(true);
    } catch (error) {
      console.error("Error generating preview:", error);
      message.error("Failed to generate email preview. Please try again.");
    }
  };
  const handleSendEmail = async (values) => {
    const selectedPOs = searchResult?.po_data?.filter((po) => selectedPoNos.includes(po.po_no)) || [];
    if (selectedPOs.length === 0) {
      message.error("Please select at least one PO to send notifications for.");
      return;
    }
    setEmailLoading(true);
    try {
      const response = await request("/api/po-import/po-notification/send-email", {
        method: "POST",
        data: {
          hq_po_id: searchResult?.hq_po_id,
          po_data: selectedPOs,
          cc_emails: values.cc_emails,
          override_email: values.override_email,
          summary: searchResult?.summary,
          custom_subject: values.custom_subject,
          custom_message: values.custom_message
        }
      });
      if (response.success) {
        message.success(response.message);
        setEmailModalVisible(false);
      } else {
        message.error(response.message || "Failed to send emails.");
      }
    } catch (error) {
      console.error("Email sending error:", error);
      message.error("An unexpected error occurred while sending emails.");
    } finally {
      setEmailLoading(false);
    }
  };
  const getStatusColor = (status) => {
    if (!status) return "default";
    const statusLower = status.toLowerCase();
    if (statusLower.includes("confirmed") || statusLower.includes("delivered") && !statusLower.includes("no delivered") || statusLower.includes("approved")) {
      return "green";
    }
    if (statusLower.includes("no delivered") || statusLower.includes("no approval") || statusLower.includes("saved")) {
      return "orange";
    }
    if (statusLower.includes("rejected") || statusLower.includes("cancelled") || statusLower.includes("terminated")) {
      return "red";
    }
    return "default";
  };
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      setSelectedPoNos(searchResult?.po_data?.map((po) => po.po_no) || []);
    } else {
      setSelectedPoNos([]);
    }
  };
  const handleSelectRow = (poNo, checked) => {
    setSelectedPoNos((prev) => {
      if (checked) {
        return [...prev, poNo];
      } else {
        return prev.filter((p) => p !== poNo);
      }
    });
  };
  const columns = [
    {
      title: /* @__PURE__ */ jsx(
        Checkbox,
        {
          indeterminate: selectedPoNos.length > 0 && selectedPoNos.length < (searchResult?.po_data?.length || 0),
          checked: selectedPoNos.length === (searchResult?.po_data?.length || 0) && (searchResult?.po_data?.length || 0) > 0,
          onChange: handleSelectAll
        }
      ),
      dataIndex: "select",
      width: 50,
      render: (_, record) => /* @__PURE__ */ jsx(
        Checkbox,
        {
          checked: selectedPoNos.includes(record.po_no),
          onChange: (e) => handleSelectRow(record.po_no, e.target.checked)
        }
      )
    },
    {
      title: "PO No",
      dataIndex: "po_no",
      key: "po_no",
      width: 120,
      sorter: (a, b) => a.po_no.localeCompare(b.po_no)
    },
    {
      title: "Branch Code",
      dataIndex: "po_branch_code",
      key: "po_branch_code",
      width: 100,
      sorter: (a, b) => (a.po_branch_code || "").localeCompare(b.po_branch_code || "")
    },
    {
      title: "Branch Name",
      dataIndex: "po_branch_name",
      key: "po_branch_name",
      width: 150,
      sorter: (a, b) => (a.po_branch_name || "").localeCompare(b.po_branch_name || "")
    },
    {
      title: "Delivered",
      dataIndex: "po_delivered",
      key: "po_delivered",
      width: 100,
      render: (delivered) => /* @__PURE__ */ jsx(Tag, { color: delivered === "DELIVERED" ? "green" : "orange", children: delivered || "N/A" })
    },
    {
      title: "Delivery Date",
      dataIndex: "delivery_date",
      key: "delivery_date",
      width: 120,
      sorter: (a, b) => {
        const dateA = a.delivery_date ? new Date(a.delivery_date).getTime() : 0;
        const dateB = b.delivery_date ? new Date(b.delivery_date).getTime() : 0;
        return dateA - dateB;
      }
    },
    {
      title: "Supplier Delivery Note",
      dataIndex: "supplier_delivery_note",
      key: "supplier_delivery_note",
      width: 200,
      ellipsis: true,
      render: (note) => note || "N/A"
    },
    {
      title: "Total Ordered Pcs",
      dataIndex: "total_po_ordered_pcs",
      key: "total_po_ordered_pcs",
      width: 150,
      align: "right",
      render: (value) => {
        if (typeof value === "string" && value.includes("No View Permission")) {
          return /* @__PURE__ */ jsx("span", { style: { color: "#ff4d4f" }, children: value });
        }
        return typeof value === "number" ? value.toLocaleString() : value || "0";
      }
    },
    {
      title: "Contact Email",
      dataIndex: "contact_email",
      key: "contact_email",
      width: 200,
      ellipsis: true
    }
  ];
  const emailModalColumns = [
    { title: "PO No", dataIndex: "po_no", key: "po_no" },
    { title: "Branch", dataIndex: "po_branch_name", key: "po_branch_name" },
    { title: "Branch Email", dataIndex: "contact_email", key: "contact_email", render: (text) => text || /* @__PURE__ */ jsx(Text, { type: "danger", children: "No Email" }) }
  ];
  return /* @__PURE__ */ jsxs(
    PageContainer,
    {
      header: {
        title: "Purchase Order Notification",
        extra: [
          /* @__PURE__ */ jsx(
            Button,
            {
              type: "primary",
              icon: /* @__PURE__ */ jsx(MailOutlined, {}),
              onClick: () => setEmailModalVisible(true),
              loading: emailLoading,
              disabled: !searchResult?.po_data || searchResult.po_data.length === 0,
              children: "Send Email Notifications"
            },
            "send-email"
          )
        ]
      },
      children: [
        /* @__PURE__ */ jsx(ProCard, { children: /* @__PURE__ */ jsxs(Form, { form: searchForm, onFinish: handleSearch, layout: "inline", children: [
          /* @__PURE__ */ jsx(Form.Item, { name: "hq_po_id", label: "Master PO No (HQ PO ID)", rules: [{ required: true, message: "Please input the HQ PO ID!" }], children: /* @__PURE__ */ jsx(Input, { placeholder: "Enter HQ PO ID (e.g., HQ34921 or 34921)", style: { width: 300 } }) }),
          /* @__PURE__ */ jsx(Form.Item, { children: /* @__PURE__ */ jsx(Button, { type: "primary", htmlType: "submit", icon: /* @__PURE__ */ jsx(SearchOutlined, {}), loading, children: "Search" }) })
        ] }) }),
        /* @__PURE__ */ jsxs(Spin, { spinning: loading, children: [
          searchResult && searchResult.success && searchResult.summary && /* @__PURE__ */ jsx(ProCard, { title: "Master PO Information", style: { marginTop: 20 }, bordered: true, children: /* @__PURE__ */ jsxs(Descriptions, { bordered: true, column: 2, children: [
            /* @__PURE__ */ jsx(Descriptions.Item, { label: "Master PO No", children: searchResult.summary.hq_po_id }),
            /* @__PURE__ */ jsx(Descriptions.Item, { label: "PO Date", children: searchResult.summary.po_date }),
            /* @__PURE__ */ jsx(Descriptions.Item, { label: "Vendor", children: searchResult.summary.vendor_name }),
            /* @__PURE__ */ jsx(Descriptions.Item, { label: "Master PO Total Qty", children: /* @__PURE__ */ jsx("strong", { children: typeof searchResult.summary.master_po_total_qty === "number" ? searchResult.summary.master_po_total_qty.toLocaleString() : searchResult.summary.master_po_total_qty }) })
          ] }) }),
          searchResult && !searchResult.success && /* @__PURE__ */ jsx(
            Alert,
            {
              message: "Search Information",
              description: searchResult.message,
              type: "warning",
              showIcon: true,
              style: { marginTop: 20 }
            }
          ),
          searchResult && searchResult.po_data && /* @__PURE__ */ jsx(ProCard, { style: { marginTop: 20 }, children: /* @__PURE__ */ jsx(
            ProTable,
            {
              headerTitle: "Branch PO Details",
              dataSource: searchResult.po_data,
              columns,
              rowKey: "po_no",
              pagination: { pageSize: 10 },
              search: false,
              toolBarRender: false,
              scroll: { x: "max-content" }
            }
          ) })
        ] }),
        /* @__PURE__ */ jsx(
          Modal,
          {
            title: "Email Notification Options",
            open: emailModalVisible,
            onCancel: () => setEmailModalVisible(false),
            confirmLoading: emailLoading,
            width: 800,
            destroyOnClose: true,
            footer: [
              /* @__PURE__ */ jsx(Button, { onClick: () => setEmailModalVisible(false), children: "Cancel" }, "cancel"),
              /* @__PURE__ */ jsx(Button, { icon: /* @__PURE__ */ jsx(EyeOutlined, {}), onClick: handlePreviewEmail, children: "Preview Email" }, "preview"),
              /* @__PURE__ */ jsx(Button, { type: "primary", loading: emailLoading, onClick: () => emailForm.submit(), children: "Send Email" }, "send")
            ],
            children: /* @__PURE__ */ jsxs(Form, { form: emailForm, layout: "vertical", onFinish: handleSendEmail, initialValues: { cc_emails: [], override_email: [], custom_subject: "", custom_message: "" }, children: [
              /* @__PURE__ */ jsx(
                ProTable,
                {
                  headerTitle: "Select Branches to Notify",
                  dataSource: searchResult?.po_data || [],
                  columns: emailModalColumns,
                  rowKey: "po_no",
                  rowSelection: {
                    selectedRowKeys: selectedPoNos,
                    onChange: (selectedRowKeys) => setSelectedPoNos(selectedRowKeys)
                  },
                  pagination: false,
                  search: false,
                  toolBarRender: false,
                  size: "small"
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "cc_emails",
                  label: "CC Recipients",
                  style: { marginTop: 20 },
                  rules: [{
                    validator: async (_, value) => {
                      if (value && value.length > 0) {
                        for (const email of value) {
                          if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                            return Promise.reject(new Error(`Invalid email address: ${email}`));
                          }
                        }
                      }
                      return Promise.resolve();
                    }
                  }],
                  children: /* @__PURE__ */ jsx(Select, { mode: "tags", placeholder: "Enter CC email addresses and press Enter", tokenSeparators: [",", " "], allowClear: true })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "override_email",
                  label: "Override Recipient",
                  help: "If filled, a single email with all selected POs will be sent to these addresses.",
                  rules: [{
                    validator: async (_, value) => {
                      if (value && value.length > 0) {
                        for (const email of value) {
                          if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                            return Promise.reject(new Error(`Invalid email address: ${email}`));
                          }
                        }
                      }
                      return Promise.resolve();
                    }
                  }],
                  children: /* @__PURE__ */ jsx(Select, { mode: "tags", placeholder: "Enter one or more email addresses to override", tokenSeparators: [",", " "], allowClear: true })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "custom_subject",
                  label: "Custom Email Subject",
                  help: "If left empty, a default subject will be used.",
                  children: /* @__PURE__ */ jsx(Input, { placeholder: "Enter custom email subject (optional)" })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "custom_message",
                  label: "Custom Message",
                  help: "This message will appear before the PO details table in the email. If left empty, a default message will be used.",
                  children: /* @__PURE__ */ jsx(
                    TextArea,
                    {
                      rows: 4,
                      placeholder: "Enter custom message to include in the email (optional)",
                      showCount: true,
                      maxLength: 1e3
                    }
                  )
                }
              )
            ] })
          }
        ),
        /* @__PURE__ */ jsxs(
          Modal,
          {
            title: "Email Preview",
            open: previewModalVisible,
            onCancel: () => setPreviewModalVisible(false),
            footer: [
              /* @__PURE__ */ jsx(Button, { onClick: () => setPreviewModalVisible(false), children: "Close" }, "close"),
              /* @__PURE__ */ jsx(Button, { type: "primary", onClick: () => {
                setPreviewModalVisible(false);
                emailForm.submit();
              }, children: "Send Email" }, "send")
            ],
            width: 1e3,
            style: { top: 20 },
            children: [
              /* @__PURE__ */ jsx(
                Alert,
                {
                  message: "Email Preview",
                  description: "This is how your email will appear to recipients. You can review the subject, custom message, and PO details before sending.",
                  type: "info",
                  showIcon: true,
                  style: { marginBottom: 16 }
                }
              ),
              /* @__PURE__ */ jsx("div", { style: { maxHeight: "65vh", overflow: "auto", border: "1px solid #d9d9d9", borderRadius: "6px", padding: "16px", backgroundColor: "#fafafa" }, children: /* @__PURE__ */ jsx("div", { dangerouslySetInnerHTML: { __html: previewContent } }) })
            ]
          }
        )
      ]
    }
  );
};
export default PONotificationPage;
